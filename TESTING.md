# Testing Documentation - <PERSON><PERSON> App

This document provides comprehensive information about the testing strategy, setup, and execution for the Potto app.

## Table of Contents

1. [Testing Strategy](#testing-strategy)
2. [Test Structure](#test-structure)
3. [Setup and Installation](#setup-and-installation)
4. [Running Tests](#running-tests)
5. [Test Categories](#test-categories)
6. [Coverage Reports](#coverage-reports)
7. [CI/CD Integration](#cicd-integration)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Testing Strategy

The Potto app follows a comprehensive testing strategy that includes:

- **Unit Tests**: Test individual functions, classes, and services in isolation
- **Widget Tests**: Test UI components and their interactions
- **Integration Tests**: Test complete user flows and feature interactions
- **Security Tests**: Test security measures, encryption, and fraud prevention
- **BLoC Tests**: Test state management and business logic

### Testing Pyramid

```
    /\
   /  \     Integration Tests (Few)
  /____\    
 /      \   Widget Tests (Some)
/________\  
|        |  Unit Tests (Many)
|________|  
```

## Test Structure

```
test/
├── test_utils/
│   ├── test_helpers.dart          # Common test utilities
│   └── mocks.dart                 # Mock classes and data builders
├── unit/
│   ├── services/
│   │   ├── security_service_test.dart
│   │   ├── fraud_prevention_service_test.dart
│   │   └── ...
│   └── utils/
│       └── validation_utils_test.dart
├── widget/
│   └── components/
│       ├── custom_button_test.dart
│       ├── custom_text_field_test.dart
│       └── ...
├── bloc/
│   └── auth/
│       └── auth_bloc_test.dart
├── integration/
│   └── auth_flow_test.dart
└── test_config.yaml
```

## Setup and Installation

### Prerequisites

1. **Flutter SDK**: Ensure Flutter is installed and in your PATH
2. **Dependencies**: Install test dependencies
3. **Test Tools**: Optional tools for enhanced testing

```bash
# Install Flutter dependencies
flutter pub get

# Install additional test tools (optional)
# For coverage reports
sudo apt-get install lcov  # Linux
brew install lcov          # macOS

# For code generation (if needed)
flutter packages pub run build_runner build
```

### Test Dependencies

The following test dependencies are included in `pubspec.yaml`:

```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.2
  bloc_test: ^9.1.4
  integration_test:
    sdk: flutter
  build_runner: ^2.4.7
```

## Running Tests

### Using the Test Runner Script

The easiest way to run tests is using the provided script:

```bash
# Run all tests with coverage
./scripts/run_tests.sh

# Run specific test categories
./scripts/run_tests.sh --unit-tests
./scripts/run_tests.sh --widget-tests
./scripts/run_tests.sh --integration-tests
./scripts/run_tests.sh --security-tests

# Run with options
./scripts/run_tests.sh --clean --verbose
./scripts/run_tests.sh --no-coverage
```

### Using Flutter Commands Directly

```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/unit/services/security_service_test.dart

# Run tests with coverage
flutter test --coverage

# Run tests with verbose output
flutter test --reporter=expanded

# Run integration tests
flutter test test/integration/
```

### Test Script Options

| Option | Description |
|--------|-------------|
| `-u, --unit-tests` | Run unit tests only |
| `-w, --widget-tests` | Run widget tests only |
| `-i, --integration-tests` | Run integration tests only |
| `-s, --security-tests` | Run security tests only |
| `-a, --all-tests` | Run all tests (default) |
| `-c, --coverage` | Generate coverage report |
| `--no-coverage` | Skip coverage generation |
| `--clean` | Clean before running tests |
| `-v, --verbose` | Verbose output |
| `-h, --help` | Show help message |

## Test Categories

### Unit Tests

Test individual components in isolation:

- **Services**: SecurityService, FraudPreventionService, etc.
- **Utils**: ValidationUtils, formatting utilities
- **Models**: Data models and entities
- **Use Cases**: Business logic components

**Example:**
```dart
test('should encrypt and decrypt data correctly', () async {
  const plainText = 'Hello, World!';
  
  final encryptedData = await SecurityService.encryptData(plainText);
  final decryptedData = await SecurityService.decryptData(encryptedData);
  
  expect(decryptedData, equals(plainText));
});
```

### Widget Tests

Test UI components and their behavior:

- **Custom Widgets**: CustomButton, CustomTextField
- **Screens**: Login, Register, Home screens
- **Forms**: Validation, user interactions

**Example:**
```dart
testWidgets('should display button with text', (WidgetTester tester) async {
  await tester.pumpWidget(
    TestHelpers.createTestWidget(
      child: CustomButton(
        text: 'Test Button',
        onPressed: () {},
      ),
    ),
  );

  expect(find.text('Test Button'), findsOneWidget);
});
```

### Integration Tests

Test complete user flows:

- **Authentication Flow**: Login, register, logout
- **Wallet Management**: Create, edit, delete wallets
- **Transaction Flow**: Create transactions, split expenses
- **Payment Integration**: Card creation, payments

### Security Tests

Test security measures:

- **Input Validation**: SQL injection, XSS prevention
- **Encryption**: Data encryption/decryption
- **Authentication**: Biometric auth, session management
- **Fraud Prevention**: Transaction limits, velocity checks

### BLoC Tests

Test state management:

- **State Transitions**: Verify correct state changes
- **Event Handling**: Test event processing
- **Error Handling**: Test error states
- **Side Effects**: Test external service calls

## Coverage Reports

### Generating Coverage

```bash
# Generate coverage with test script
./scripts/run_tests.sh --coverage

# Generate coverage manually
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

### Coverage Targets

- **Minimum Coverage**: 80%
- **Target Coverage**: 90%
- **Critical Components**: 95%+ (Security, Payment)

### Viewing Coverage

- **HTML Report**: Open `coverage/html/index.html` in browser
- **LCOV Data**: Raw data in `coverage/lcov.info`
- **Console Summary**: Displayed after test run

## CI/CD Integration

### GitHub Actions

The project includes GitHub Actions workflow for automated testing:

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter pub get
      - run: ./scripts/run_tests.sh --all-tests --coverage
```

### Test Matrix

Tests run on multiple Flutter versions:
- Flutter 3.16.0
- Flutter 3.19.0 (latest stable)

## Best Practices

### Writing Tests

1. **Follow AAA Pattern**: Arrange, Act, Assert
2. **Use Descriptive Names**: Test names should describe the scenario
3. **Test One Thing**: Each test should verify one specific behavior
4. **Use Mocks**: Mock external dependencies
5. **Clean Up**: Dispose resources in tearDown

### Test Organization

1. **Group Related Tests**: Use `group()` to organize tests
2. **Shared Setup**: Use `setUp()` and `tearDown()` for common setup
3. **Test Data**: Use builders for consistent test data
4. **Constants**: Define test constants for reusability

### Mock Usage

```dart
// Good: Mock external dependencies
class MockApiClient extends Mock implements ApiClient {}

// Good: Use test data builders
final mockUser = MockDataBuilder.createMockUser(
  email: '<EMAIL>',
  fullName: 'Test User',
);
```

### Performance

1. **Parallel Execution**: Tests run in parallel when possible
2. **Resource Management**: Properly dispose of resources
3. **Test Isolation**: Tests should not depend on each other
4. **Fast Feedback**: Unit tests should run quickly

## Troubleshooting

### Common Issues

#### Tests Failing Due to Missing Dependencies

```bash
# Solution: Get dependencies
flutter pub get
flutter packages pub run build_runner build
```

#### Mock Generation Issues

```bash
# Solution: Regenerate mocks
flutter packages pub run build_runner build --delete-conflicting-outputs
```

#### Coverage Not Generated

```bash
# Solution: Install lcov
sudo apt-get install lcov  # Linux
brew install lcov          # macOS
```

#### Integration Tests Timing Out

```bash
# Solution: Increase timeout in test_config.yaml
integration_test_timeout: 180000  # 3 minutes
```

### Debug Mode

Run tests with verbose output for debugging:

```bash
./scripts/run_tests.sh --verbose
flutter test --reporter=expanded
```

### Test Environment Issues

1. **Clean Project**: Use `--clean` flag
2. **Check Flutter Version**: Ensure compatible Flutter version
3. **Verify Dependencies**: Check pubspec.yaml dependencies
4. **Mock Services**: Ensure mock services are properly configured

### Getting Help

1. **Check Logs**: Review test output for specific errors
2. **Documentation**: Refer to Flutter testing documentation
3. **Team Support**: Contact the development team
4. **Issue Tracking**: Create issues for persistent problems

## Continuous Improvement

### Metrics Tracking

- Test execution time
- Coverage percentage
- Test reliability
- Flaky test identification

### Regular Maintenance

- Update test dependencies
- Review and refactor tests
- Add tests for new features
- Remove obsolete tests

### Quality Gates

- All tests must pass before merge
- Coverage must meet minimum threshold
- Security tests must pass
- Performance tests within limits

---

For more information about specific test implementations, refer to the individual test files and their documentation comments.
