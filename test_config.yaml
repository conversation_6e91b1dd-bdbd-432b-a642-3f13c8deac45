# Test Configuration for Potto App
# This file defines test settings and configurations

# Test Environment Settings
test_environment:
  # Mock data settings
  use_mock_data: true
  mock_user_id: "test-user-123"
  mock_email: "<EMAIL>"
  
  # Test database settings
  use_test_database: true
  test_database_url: "postgresql://test:test@localhost:5432/potto_test"
  
  # API settings for testing
  mock_api_responses: true
  api_timeout: 5000 # milliseconds
  
  # Security settings for testing
  skip_biometric_auth: true
  use_test_encryption_keys: true
  
  # Payment testing settings
  use_stripe_test_keys: true
  mock_payment_responses: true

# Test Coverage Settings
coverage:
  minimum_coverage: 80
  exclude_files:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/main.dart"
    - "test/**"
  
  # Coverage reports
  generate_html_report: true
  generate_lcov_report: true

# Test Execution Settings
execution:
  # Timeout settings
  test_timeout: 30000 # milliseconds
  widget_test_timeout: 60000 # milliseconds
  integration_test_timeout: 120000 # milliseconds
  
  # Parallel execution
  enable_parallel_execution: true
  max_concurrent_tests: 4
  
  # Test ordering
  randomize_test_order: true
  
  # Retry settings
  retry_failed_tests: true
  max_retries: 2

# Mock Services Configuration
mock_services:
  supabase:
    enabled: true
    mock_auth_responses: true
    mock_database_responses: true
    mock_realtime_responses: true
  
  stripe:
    enabled: true
    use_test_keys: true
    mock_payment_intents: true
    mock_card_creation: true
  
  notifications:
    enabled: true
    mock_push_notifications: true
    mock_local_notifications: true
  
  biometric_auth:
    enabled: true
    always_succeed: false # Set to true to always succeed biometric auth
    simulate_unavailable: false # Set to true to simulate unavailable biometric

# Test Data Configuration
test_data:
  # User test data
  users:
    - id: "user-1"
      email: "<EMAIL>"
      full_name: "Test User 1"
      avatar_url: "https://example.com/avatar1.jpg"
    
    - id: "user-2"
      email: "<EMAIL>"
      full_name: "Test User 2"
      avatar_url: "https://example.com/avatar2.jpg"
  
  # Wallet test data
  wallets:
    - id: "wallet-1"
      name: "Test Wallet 1"
      description: "Test wallet for unit tests"
      balance: 1000.00
      owner_id: "user-1"
      currency: "USD"
    
    - id: "wallet-2"
      name: "Test Wallet 2"
      description: "Another test wallet"
      balance: 500.00
      owner_id: "user-2"
      currency: "EUR"
  
  # Transaction test data
  transactions:
    - id: "txn-1"
      wallet_id: "wallet-1"
      user_id: "user-1"
      amount: 50.00
      description: "Test transaction 1"
      type: "expense"
      category: "food"
    
    - id: "txn-2"
      wallet_id: "wallet-1"
      user_id: "user-2"
      amount: 25.00
      description: "Test transaction 2"
      type: "expense"
      category: "transport"

# Widget Test Configuration
widget_tests:
  # Pump and settle settings
  default_pump_duration: 100 # milliseconds
  max_pump_duration: 5000 # milliseconds
  
  # Finder settings
  finder_timeout: 5000 # milliseconds
  
  # Gesture settings
  tap_delay: 100 # milliseconds
  scroll_duration: 300 # milliseconds
  
  # Theme testing
  test_light_theme: true
  test_dark_theme: true
  test_custom_themes: false

# Integration Test Configuration
integration_tests:
  # Device settings
  test_on_multiple_screen_sizes: true
  screen_sizes:
    - width: 375
      height: 667
      name: "iPhone SE"
    - width: 414
      height: 896
      name: "iPhone 11"
    - width: 360
      height: 640
      name: "Android Medium"
  
  # Platform testing
  test_android: true
  test_ios: true
  
  # Network testing
  test_offline_mode: true
  test_slow_network: true
  simulate_network_errors: true
  
  # Performance testing
  measure_performance: true
  performance_thresholds:
    app_startup_time: 3000 # milliseconds
    screen_transition_time: 500 # milliseconds
    api_response_time: 2000 # milliseconds

# Security Test Configuration
security_tests:
  # Input validation testing
  test_sql_injection: true
  test_xss_attacks: true
  test_input_sanitization: true
  
  # Authentication testing
  test_auth_bypass: true
  test_session_management: true
  test_password_policies: true
  
  # Encryption testing
  test_data_encryption: true
  test_secure_storage: true
  test_key_management: true
  
  # Fraud prevention testing
  test_transaction_limits: true
  test_velocity_checks: true
  test_suspicious_activity: true

# Performance Test Configuration
performance_tests:
  # Memory testing
  monitor_memory_usage: true
  memory_leak_detection: true
  max_memory_usage: 512 # MB
  
  # CPU testing
  monitor_cpu_usage: true
  max_cpu_usage: 80 # percentage
  
  # Battery testing
  monitor_battery_usage: true
  
  # Network testing
  monitor_network_usage: true
  max_network_requests_per_minute: 100

# Reporting Configuration
reporting:
  # Test results
  generate_junit_xml: true
  generate_json_report: true
  
  # Screenshots
  capture_screenshots_on_failure: true
  screenshot_format: "png"
  
  # Logs
  capture_logs: true
  log_level: "info" # debug, info, warning, error
  
  # Artifacts
  save_test_artifacts: true
  artifacts_directory: "test_artifacts"
  
  # Notifications
  notify_on_test_completion: false
  notification_webhook: ""

# CI/CD Integration
ci_cd:
  # GitHub Actions
  github_actions:
    enabled: true
    run_on_pr: true
    run_on_push: true
    cache_dependencies: true
  
  # Test matrix
  test_matrix:
    flutter_versions: ["3.16.0", "3.19.0"]
    dart_versions: ["3.2.0", "3.3.0"]
  
  # Deployment gates
  require_tests_pass: true
  minimum_coverage_for_deployment: 85
  
  # Parallel execution in CI
  parallel_jobs: 4
  job_timeout: 30 # minutes
