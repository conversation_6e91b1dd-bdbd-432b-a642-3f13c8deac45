import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/errors/exceptions.dart' as app_exceptions;
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> login(String email, String password);
  Future<UserModel> register(String email, String password,
      {String? firstName, String? lastName});
  Future<void> logout();
  Future<UserModel?> getCurrentUser();
  Future<void> resetPassword(String email);
  Future<UserModel> updateProfile(UserModel user);
  Future<void> deleteAccount();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final SupabaseClient _supabaseClient;

  AuthRemoteDataSourceImpl(this._supabaseClient);

  @override
  Future<UserModel> login(String email, String password) async {
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw const app_exceptions.AuthException(
          message: 'Login failed',
          code: 401,
        );
      }

      // Get user profile data
      final userProfile = await _getUserProfile(response.user!.id);
      return userProfile;
    } on AuthException catch (e) {
      throw app_exceptions.AuthException(
        message: e.message,
        code: 401,
      );
    } catch (e) {
      throw app_exceptions.AuthException(
        message: 'Login failed: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<UserModel> register(
    String email,
    String password, {
    String? firstName,
    String? lastName,
  }) async {
    try {
      final response = await _supabaseClient.auth.signUp(
        email: email,
        password: password,
        data: {
          'first_name': firstName,
          'last_name': lastName,
        },
      );

      if (response.user == null) {
        throw const app_exceptions.AuthException(
          message: 'Registration failed',
          code: 400,
        );
      }

      // Update user profile with additional data
      await _supabaseClient.from('users').upsert({
        'id': response.user!.id,
        'email': email,
        'first_name': firstName,
        'last_name': lastName,
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Get complete user profile
      final userProfile = await _getUserProfile(response.user!.id);
      return userProfile;
    } on AuthException catch (e) {
      throw app_exceptions.AuthException(
        message: e.message,
        code: 400,
      );
    } catch (e) {
      throw app_exceptions.AuthException(
        message: 'Registration failed: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      await _supabaseClient.auth.signOut();
    } catch (e) {
      throw app_exceptions.AuthException(
        message: 'Logout failed: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) return null;

      return await _getUserProfile(user.id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> resetPassword(String email) async {
    try {
      await _supabaseClient.auth.resetPasswordForEmail(email);
    } on AuthException catch (e) {
      throw app_exceptions.AuthException(
        message: e.message,
        code: 400,
      );
    } catch (e) {
      throw app_exceptions.AuthException(
        message: 'Password reset failed: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<UserModel> updateProfile(UserModel user) async {
    try {
      // Update auth user metadata
      await _supabaseClient.auth.updateUser(
        UserAttributes(
          email: user.email,
          data: {
            'first_name': user.firstName,
            'last_name': user.lastName,
          },
        ),
      );

      // Update user profile in database
      await _supabaseClient.from('users').update({
        'first_name': user.firstName,
        'last_name': user.lastName,
        'phone_number': user.phoneNumber,
        'avatar_url': user.avatarUrl,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', user.id);

      // Update user preferences
      await _supabaseClient.from('user_preferences').update({
        'notifications_enabled': user.preferences.notificationsEnabled,
        'email_notifications_enabled':
            user.preferences.emailNotificationsEnabled,
        'push_notifications_enabled': user.preferences.pushNotificationsEnabled,
        'transaction_notifications_enabled':
            user.preferences.transactionNotificationsEnabled,
        'invitation_notifications_enabled':
            user.preferences.invitationNotificationsEnabled,
        'currency': user.preferences.currency,
        'language': user.preferences.language,
        'timezone': user.preferences.timezone,
        'biometric_enabled': user.preferences.biometricEnabled,
        'dark_mode_enabled': user.preferences.darkModeEnabled,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('user_id', user.id);

      return await _getUserProfile(user.id);
    } catch (e) {
      throw app_exceptions.AuthException(
        message: 'Profile update failed: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw const app_exceptions.AuthException(
          message: 'No user logged in',
          code: 401,
        );
      }

      // Delete user data (cascade will handle related data)
      await _supabaseClient.from('users').delete().eq('id', user.id);

      // Sign out
      await _supabaseClient.auth.signOut();
    } catch (e) {
      throw app_exceptions.AuthException(
        message: 'Account deletion failed: ${e.toString()}',
        code: 500,
      );
    }
  }

  Future<UserModel> _getUserProfile(String userId) async {
    final response = await _supabaseClient.from('users').select('''
          *,
          user_preferences (*)
        ''').eq('id', userId).single();

    final preferencesData =
        response['user_preferences'] as Map<String, dynamic>?;
    final preferences = preferencesData != null
        ? UserPreferencesModel.fromJson(preferencesData)
        : const UserPreferencesModel(
            notificationsEnabled: true,
            emailNotificationsEnabled: true,
            pushNotificationsEnabled: true,
            transactionNotificationsEnabled: true,
            invitationNotificationsEnabled: true,
            currency: 'USD',
            language: 'en',
            timezone: 'UTC',
            biometricEnabled: false,
            darkModeEnabled: false,
          );

    return UserModel(
      id: response['id'],
      email: response['email'],
      firstName: response['first_name'],
      lastName: response['last_name'],
      phoneNumber: response['phone_number'],
      avatarUrl: response['avatar_url'],
      createdAt: DateTime.parse(response['created_at']),
      updatedAt: DateTime.parse(response['updated_at']),
      isEmailVerified: response['is_email_verified'] ?? false,
      isPhoneVerified: response['is_phone_verified'] ?? false,
      preferences: preferences,
    );
  }
}
