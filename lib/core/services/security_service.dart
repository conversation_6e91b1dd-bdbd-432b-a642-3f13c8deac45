import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:pointycastle/export.dart';

/// Comprehensive security service for encryption, secure storage, and biometric authentication
class SecurityService {
  static const _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static final LocalAuthentication _localAuth = LocalAuthentication();
  static final Random _random = Random.secure();

  // Storage Keys
  static const String _encryptionKeyKey = 'encryption_key';
  static const String _saltKey = 'salt';
  static const String _biometricEnabledKey = 'biometric_enabled';

  /// Initialize security service
  static Future<void> initialize() async {
    await _ensureEncryptionKey();
  }

  /// Generate and store encryption key if not exists
  static Future<void> _ensureEncryptionKey() async {
    final existingKey = await _secureStorage.read(key: _encryptionKeyKey);
    if (existingKey == null) {
      final key = _generateSecureKey();
      final salt = _generateSalt();

      await _secureStorage.write(
          key: _encryptionKeyKey, value: base64Encode(key));
      await _secureStorage.write(key: _saltKey, value: base64Encode(salt));
    }
  }

  /// Generate a secure 256-bit key
  static Uint8List _generateSecureKey() {
    final key = Uint8List(32); // 256 bits
    for (int i = 0; i < key.length; i++) {
      key[i] = _random.nextInt(256);
    }
    return key;
  }

  /// Generate a secure salt
  static Uint8List _generateSalt() {
    final salt = Uint8List(16); // 128 bits
    for (int i = 0; i < salt.length; i++) {
      salt[i] = _random.nextInt(256);
    }
    return salt;
  }

  /// Get encryption key
  static Future<Uint8List> _getEncryptionKey() async {
    final keyString = await _secureStorage.read(key: _encryptionKeyKey);
    if (keyString == null) {
      throw Exception('Encryption key not found');
    }
    return base64Decode(keyString);
  }

  /// Get salt
  static Future<Uint8List> _getSalt() async {
    final saltString = await _secureStorage.read(key: _saltKey);
    if (saltString == null) {
      throw Exception('Salt not found');
    }
    return base64Decode(saltString);
  }

  /// Encrypt sensitive data using AES-256-GCM
  static Future<String> encryptData(String plainText) async {
    try {
      final key = await _getEncryptionKey();
      final salt = await _getSalt();

      // Generate IV
      final iv = Uint8List(12); // 96 bits for GCM
      for (int i = 0; i < iv.length; i++) {
        iv[i] = _random.nextInt(256);
      }

      // Create cipher
      final cipher = GCMBlockCipher(AESEngine());
      final params = AEADParameters(KeyParameter(key), 128, iv, salt);
      cipher.init(true, params);

      // Encrypt
      final plainBytes = utf8.encode(plainText);
      final encryptedBytes = cipher.process(Uint8List.fromList(plainBytes));

      // Combine IV + encrypted data
      final result = Uint8List(iv.length + encryptedBytes.length);
      result.setRange(0, iv.length, iv);
      result.setRange(iv.length, result.length, encryptedBytes);

      return base64Encode(result);
    } catch (e) {
      throw Exception('Encryption failed: $e');
    }
  }

  /// Decrypt sensitive data using AES-256-GCM
  static Future<String> decryptData(String encryptedData) async {
    try {
      final key = await _getEncryptionKey();
      final salt = await _getSalt();
      final data = base64Decode(encryptedData);

      // Extract IV and encrypted data
      final iv = data.sublist(0, 12);
      final encryptedBytes = data.sublist(12);

      // Create cipher
      final cipher = GCMBlockCipher(AESEngine());
      final params = AEADParameters(KeyParameter(key), 128, iv, salt);
      cipher.init(false, params);

      // Decrypt
      final decryptedBytes = cipher.process(encryptedBytes);
      return utf8.decode(decryptedBytes);
    } catch (e) {
      throw Exception('Decryption failed: $e');
    }
  }

  /// Hash sensitive data using SHA-256
  static String hashData(String data, {String? salt}) {
    final bytes = utf8.encode(data + (salt ?? ''));
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Generate secure random string
  static String generateSecureToken({int length = 32}) {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    return List.generate(
        length, (index) => chars[_random.nextInt(chars.length)]).join();
  }

  /// Store sensitive data securely
  static Future<void> storeSecureData(String key, String value) async {
    final encryptedValue = await encryptData(value);
    await _secureStorage.write(key: key, value: encryptedValue);
  }

  /// Retrieve sensitive data securely
  static Future<String?> getSecureData(String key) async {
    final encryptedValue = await _secureStorage.read(key: key);
    if (encryptedValue == null) return null;

    try {
      return await decryptData(encryptedValue);
    } catch (e) {
      debugPrint('Failed to decrypt data for key $key: $e');
      return null;
    }
  }

  /// Delete secure data
  static Future<void> deleteSecureData(String key) async {
    await _secureStorage.delete(key: key);
  }

  /// Clear all secure data
  static Future<void> clearAllSecureData() async {
    await _secureStorage.deleteAll();
  }

  /// Check if biometric authentication is available
  static Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      debugPrint('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Authenticate using biometrics
  static Future<bool> authenticateWithBiometrics({
    String localizedReason = 'Please authenticate to access your account',
  }) async {
    try {
      final isAvailable = await isBiometricAvailable();
      if (!isAvailable) return false;

      return await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
    } catch (e) {
      debugPrint('Biometric authentication error: $e');
      return false;
    }
  }

  /// Enable biometric authentication
  static Future<bool> enableBiometricAuth() async {
    final success = await authenticateWithBiometrics(
      localizedReason: 'Authenticate to enable biometric login',
    );

    if (success) {
      await _secureStorage.write(key: _biometricEnabledKey, value: 'true');
    }

    return success;
  }

  /// Disable biometric authentication
  static Future<void> disableBiometricAuth() async {
    await _secureStorage.delete(key: _biometricEnabledKey);
  }

  /// Check if biometric authentication is enabled
  static Future<bool> isBiometricEnabled() async {
    final enabled = await _secureStorage.read(key: _biometricEnabledKey);
    return enabled == 'true';
  }

  /// Validate input for potential security threats
  static bool isInputSafe(String input) {
    // Check for common injection patterns
    final dangerousPatterns = [
      RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false),
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'on\w+\s*=', caseSensitive: false),
      RegExp(r'(union|select|insert|update|delete|drop|create|alter)\s+',
          caseSensitive: false),
      RegExp('[\'";].*(-{2}|/\\*|\\*/)', caseSensitive: false),
    ];

    for (final pattern in dangerousPatterns) {
      if (pattern.hasMatch(input)) {
        return false;
      }
    }

    return true;
  }

  /// Sanitize input string
  static String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll(RegExp('[<>"' "' ]"), '') // Remove dangerous characters
        .trim();
  }

  /// Generate secure PIN
  static String generateSecurePIN({int length = 6}) {
    return List.generate(length, (index) => _random.nextInt(10)).join();
  }

  /// Validate password strength
  static bool isPasswordStrong(String password) {
    if (password.length < 8) return false;

    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters =
        password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  /// Get password strength score (0-4)
  static int getPasswordStrength(String password) {
    int score = 0;

    if (password.length >= 8) score++;
    if (password.contains(RegExp(r'[A-Z]'))) score++;
    if (password.contains(RegExp(r'[a-z]'))) score++;
    if (password.contains(RegExp(r'[0-9]'))) score++;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++;

    return score;
  }
}
