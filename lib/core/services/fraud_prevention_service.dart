import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Transaction risk assessment result
class RiskAssessment {
  final double riskScore; // 0.0 (low) to 1.0 (high)
  final bool isHighRisk;
  final List<String> riskFactors;
  final bool shouldBlock;
  final String? blockReason;

  const RiskAssessment({
    required this.riskScore,
    required this.isHighRisk,
    required this.riskFactors,
    required this.shouldBlock,
    this.blockReason,
  });
}

/// Transaction data for fraud analysis
class TransactionData {
  final String id;
  final double amount;
  final String currency;
  final String merchantName;
  final String merchantCategory;
  final DateTime timestamp;
  final String? location;
  final String cardId;
  final String userId;
  final Map<String, dynamic>? metadata;

  const TransactionData({
    required this.id,
    required this.amount,
    required this.currency,
    required this.merchantName,
    required this.merchantCategory,
    required this.timestamp,
    this.location,
    required this.cardId,
    required this.userId,
    this.metadata,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'amount': amount,
        'currency': currency,
        'merchantName': merchantName,
        'merchantCategory': merchantCategory,
        'timestamp': timestamp.toIso8601String(),
        'location': location,
        'cardId': cardId,
        'userId': userId,
        'metadata': metadata,
      };

  factory TransactionData.fromJson(Map<String, dynamic> json) =>
      TransactionData(
        id: json['id'],
        amount: json['amount'].toDouble(),
        currency: json['currency'],
        merchantName: json['merchantName'],
        merchantCategory: json['merchantCategory'],
        timestamp: DateTime.parse(json['timestamp']),
        location: json['location'],
        cardId: json['cardId'],
        userId: json['userId'],
        metadata: json['metadata'],
      );
}

/// Service for fraud detection and prevention
class FraudPreventionService {
  static const String _transactionHistoryKey = 'transaction_history';
  static const String _deviceFingerprintKey = 'device_fingerprint';
  static const String _suspiciousActivityKey = 'suspicious_activity';
  static const String _riskScoreKey = 'risk_score';

  static const int _maxTransactionsPerHour = 10;
  static const int _maxTransactionsPerDay = 50;
  static const double _maxAmountPerTransaction = 5000.0;
  static const double _maxAmountPerDay = 10000.0;
  static const int _maxFailedAttemptsPerHour = 5;

  final SharedPreferences _prefs;

  FraudPreventionService(this._prefs);

  /// Assess transaction risk
  Future<RiskAssessment> assessTransactionRisk(
      TransactionData transaction) async {
    final riskFactors = <String>[];
    double riskScore = 0.0;

    // Check transaction amount
    final amountRisk = _assessAmountRisk(transaction.amount);
    riskScore += amountRisk.score;
    riskFactors.addAll(amountRisk.factors);

    // Check transaction frequency
    final frequencyRisk = await _assessFrequencyRisk(transaction);
    riskScore += frequencyRisk.score;
    riskFactors.addAll(frequencyRisk.factors);

    // Check merchant category
    final merchantRisk = _assessMerchantRisk(transaction.merchantCategory);
    riskScore += merchantRisk.score;
    riskFactors.addAll(merchantRisk.factors);

    // Check time patterns
    final timeRisk = _assessTimeRisk(transaction.timestamp);
    riskScore += timeRisk.score;
    riskFactors.addAll(timeRisk.factors);

    // Check velocity patterns
    final velocityRisk = await _assessVelocityRisk(transaction);
    riskScore += velocityRisk.score;
    riskFactors.addAll(velocityRisk.factors);

    // Normalize risk score (0.0 to 1.0)
    riskScore = math.min(1.0, riskScore / 5.0);

    final isHighRisk = riskScore > 0.7;
    final shouldBlock = riskScore > 0.9;
    final blockReason = shouldBlock ? 'High fraud risk detected' : null;

    // Store risk assessment
    await _storeRiskScore(transaction.userId, riskScore);

    return RiskAssessment(
      riskScore: riskScore,
      isHighRisk: isHighRisk,
      riskFactors: riskFactors,
      shouldBlock: shouldBlock,
      blockReason: blockReason,
    );
  }

  /// Assess amount-based risk
  _RiskResult _assessAmountRisk(double amount) {
    final factors = <String>[];
    double score = 0.0;

    if (amount > _maxAmountPerTransaction) {
      factors.add('Transaction amount exceeds limit');
      score += 0.8;
    } else if (amount > _maxAmountPerTransaction * 0.8) {
      factors.add('High transaction amount');
      score += 0.4;
    }

    // Check for round numbers (potential fraud indicator)
    if (amount % 100 == 0 && amount > 500) {
      factors.add('Round number transaction');
      score += 0.2;
    }

    return _RiskResult(score, factors);
  }

  /// Assess frequency-based risk
  Future<_RiskResult> _assessFrequencyRisk(TransactionData transaction) async {
    final factors = <String>[];
    double score = 0.0;

    final history = await _getTransactionHistory();
    final now = DateTime.now();
    final oneHourAgo = now.subtract(const Duration(hours: 1));
    final oneDayAgo = now.subtract(const Duration(days: 1));

    // Count transactions in the last hour
    final hourlyTransactions = history
        .where((t) =>
            t.timestamp.isAfter(oneHourAgo) && t.userId == transaction.userId)
        .length;

    if (hourlyTransactions >= _maxTransactionsPerHour) {
      factors.add('Too many transactions per hour');
      score += 0.9;
    } else if (hourlyTransactions >= _maxTransactionsPerHour * 0.8) {
      factors.add('High transaction frequency');
      score += 0.5;
    }

    // Count transactions in the last day
    final dailyTransactions = history
        .where((t) =>
            t.timestamp.isAfter(oneDayAgo) && t.userId == transaction.userId)
        .length;

    if (dailyTransactions >= _maxTransactionsPerDay) {
      factors.add('Too many transactions per day');
      score += 0.8;
    }

    return _RiskResult(score, factors);
  }

  /// Assess merchant-based risk
  _RiskResult _assessMerchantRisk(String merchantCategory) {
    final factors = <String>[];
    double score = 0.0;

    // High-risk merchant categories
    const highRiskCategories = [
      'gambling',
      'adult_entertainment',
      'cryptocurrency',
      'money_transfer',
      'cash_advance',
    ];

    // Medium-risk merchant categories
    const mediumRiskCategories = [
      'online_gaming',
      'digital_goods',
      'subscription_services',
    ];

    if (highRiskCategories.contains(merchantCategory.toLowerCase())) {
      factors.add('High-risk merchant category');
      score += 0.6;
    } else if (mediumRiskCategories.contains(merchantCategory.toLowerCase())) {
      factors.add('Medium-risk merchant category');
      score += 0.3;
    }

    return _RiskResult(score, factors);
  }

  /// Assess time-based risk
  _RiskResult _assessTimeRisk(DateTime timestamp) {
    final factors = <String>[];
    double score = 0.0;

    final hour = timestamp.hour;

    // Transactions during unusual hours (2 AM - 6 AM)
    if (hour >= 2 && hour <= 6) {
      factors.add('Transaction during unusual hours');
      score += 0.3;
    }

    // Weekend transactions (higher risk for some categories)
    if (timestamp.weekday >= 6) {
      factors.add('Weekend transaction');
      score += 0.1;
    }

    return _RiskResult(score, factors);
  }

  /// Assess velocity-based risk
  Future<_RiskResult> _assessVelocityRisk(TransactionData transaction) async {
    final factors = <String>[];
    double score = 0.0;

    final history = await _getTransactionHistory();
    final now = DateTime.now();
    final oneDayAgo = now.subtract(const Duration(days: 1));

    // Calculate daily spending
    final dailySpending = history
        .where((t) =>
            t.timestamp.isAfter(oneDayAgo) && t.userId == transaction.userId)
        .fold(0.0, (sum, t) => sum + t.amount);

    if (dailySpending + transaction.amount > _maxAmountPerDay) {
      factors.add('Daily spending limit exceeded');
      score += 0.8;
    } else if (dailySpending + transaction.amount > _maxAmountPerDay * 0.8) {
      factors.add('High daily spending');
      score += 0.4;
    }

    // Check for rapid successive transactions
    final recentTransactions = history
        .where((t) =>
            t.timestamp.isAfter(now.subtract(const Duration(minutes: 5))))
        .where((t) => t.userId == transaction.userId)
        .toList();

    if (recentTransactions.length >= 3) {
      factors.add('Rapid successive transactions');
      score += 0.6;
    }

    return _RiskResult(score, factors);
  }

  /// Record transaction for fraud analysis
  Future<void> recordTransaction(TransactionData transaction) async {
    final history = await _getTransactionHistory();
    history.add(transaction);

    // Keep only last 1000 transactions
    if (history.length > 1000) {
      history.removeRange(0, history.length - 1000);
    }

    await _saveTransactionHistory(history);
  }

  /// Record failed authentication attempt
  Future<void> recordFailedAttempt(String userId) async {
    final key = '${_suspiciousActivityKey}_$userId';
    final attempts = _prefs.getStringList(key) ?? [];
    attempts.add(DateTime.now().toIso8601String());

    // Keep only attempts from the last hour
    final oneHourAgo = DateTime.now().subtract(const Duration(hours: 1));
    final recentAttempts = attempts
        .map((a) => DateTime.parse(a))
        .where((a) => a.isAfter(oneHourAgo))
        .map((a) => a.toIso8601String())
        .toList();

    await _prefs.setStringList(key, recentAttempts);
  }

  /// Check if user is temporarily blocked due to failed attempts
  Future<bool> isUserBlocked(String userId) async {
    final key = '${_suspiciousActivityKey}_$userId';
    final attempts = _prefs.getStringList(key) ?? [];

    if (attempts.length >= _maxFailedAttemptsPerHour) {
      return true;
    }

    return false;
  }

  /// Get transaction history
  Future<List<TransactionData>> _getTransactionHistory() async {
    final historyJson = _prefs.getString(_transactionHistoryKey);
    if (historyJson == null) return [];

    try {
      final List<dynamic> historyList = jsonDecode(historyJson);
      return historyList.map((json) => TransactionData.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error loading transaction history: $e');
      return [];
    }
  }

  /// Save transaction history
  Future<void> _saveTransactionHistory(List<TransactionData> history) async {
    final historyJson = jsonEncode(history.map((t) => t.toJson()).toList());
    await _prefs.setString(_transactionHistoryKey, historyJson);
  }

  /// Store risk score for user
  Future<void> _storeRiskScore(String userId, double riskScore) async {
    final key = '${_riskScoreKey}_$userId';
    await _prefs.setDouble(key, riskScore);
  }

  /// Get user's current risk score
  Future<double> getUserRiskScore(String userId) async {
    final key = '${_riskScoreKey}_$userId';
    return _prefs.getDouble(key) ?? 0.0;
  }

  /// Clear fraud data for user (on logout)
  Future<void> clearUserFraudData(String userId) async {
    await _prefs.remove('${_suspiciousActivityKey}_$userId');
    await _prefs.remove('${_riskScoreKey}_$userId');
  }
}

/// Internal risk result class
class _RiskResult {
  final double score;
  final List<String> factors;

  _RiskResult(this.score, this.factors);
}
