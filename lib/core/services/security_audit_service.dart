import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../config/security_config.dart';

/// Security audit event
class AuditEvent {
  final String id;
  final String eventType;
  final String userId;
  final String? sessionId;
  final DateTime timestamp;
  final String description;
  final Map<String, dynamic> metadata;
  final String severity; // low, medium, high, critical
  final String? ipAddress;
  final String? userAgent;
  final String? deviceId;

  const AuditEvent({
    required this.id,
    required this.eventType,
    required this.userId,
    this.sessionId,
    required this.timestamp,
    required this.description,
    required this.metadata,
    required this.severity,
    this.ipAddress,
    this.userAgent,
    this.deviceId,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'event_type': eventType,
        'user_id': userId,
        'session_id': sessionId,
        'timestamp': timestamp.toIso8601String(),
        'description': description,
        'metadata': metadata,
        'severity': severity,
        'ip_address': ipAddress,
        'user_agent': userAgent,
        'device_id': deviceId,
      };

  factory AuditEvent.fromJson(Map<String, dynamic> json) => AuditEvent(
        id: json['id'],
        eventType: json['event_type'],
        userId: json['user_id'],
        sessionId: json['session_id'],
        timestamp: DateTime.parse(json['timestamp']),
        description: json['description'],
        metadata: json['metadata'] ?? {},
        severity: json['severity'],
        ipAddress: json['ip_address'],
        userAgent: json['user_agent'],
        deviceId: json['device_id'],
      );
}

/// Security compliance status
class ComplianceStatus {
  final bool pciCompliant;
  final bool gdprCompliant;
  final bool ccpaCompliant;
  final DateTime lastAssessment;
  final List<String> violations;
  final List<String> recommendations;

  const ComplianceStatus({
    required this.pciCompliant,
    required this.gdprCompliant,
    required this.ccpaCompliant,
    required this.lastAssessment,
    required this.violations,
    required this.recommendations,
  });

  Map<String, dynamic> toJson() => {
        'pci_compliant': pciCompliant,
        'gdpr_compliant': gdprCompliant,
        'ccpa_compliant': ccpaCompliant,
        'last_assessment': lastAssessment.toIso8601String(),
        'violations': violations,
        'recommendations': recommendations,
      };

  factory ComplianceStatus.fromJson(Map<String, dynamic> json) =>
      ComplianceStatus(
        pciCompliant: json['pci_compliant'],
        gdprCompliant: json['gdpr_compliant'],
        ccpaCompliant: json['ccpa_compliant'],
        lastAssessment: DateTime.parse(json['last_assessment']),
        violations: List<String>.from(json['violations'] ?? []),
        recommendations: List<String>.from(json['recommendations'] ?? []),
      );
}

/// Service for security auditing and compliance monitoring
class SecurityAuditService {
  static const String _auditLogKey = 'security_audit_log';
  static const String _complianceStatusKey = 'compliance_status';

  final SharedPreferences _prefs;
  final SupabaseClient _supabaseClient;

  SecurityAuditService(this._prefs, this._supabaseClient);

  /// Log security audit event
  Future<void> logAuditEvent({
    required String eventType,
    required String userId,
    String? sessionId,
    required String description,
    Map<String, dynamic>? metadata,
    String severity = 'medium',
    String? ipAddress,
    String? userAgent,
    String? deviceId,
  }) async {
    if (!SecurityConfig.enableAuditLogging) return;

    final event = AuditEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      eventType: eventType,
      userId: userId,
      sessionId: sessionId,
      timestamp: DateTime.now(),
      description: description,
      metadata: metadata ?? {},
      severity: severity,
      ipAddress: ipAddress,
      userAgent: userAgent,
      deviceId: deviceId,
    );

    // Store locally
    await _storeAuditEventLocally(event);

    // Send to remote audit service if available
    if (!kDebugMode) {
      await _sendAuditEventToRemote(event);
    }

    // Check for critical events
    if (severity == 'critical') {
      await _handleCriticalEvent(event);
    }
  }

  /// Store audit event locally
  Future<void> _storeAuditEventLocally(AuditEvent event) async {
    try {
      final existingEvents = await _getLocalAuditEvents();
      existingEvents.add(event);

      // Keep only recent events (within retention period)
      final cutoffDate =
          DateTime.now().subtract(SecurityConfig.auditLogRetention);
      final filteredEvents =
          existingEvents.where((e) => e.timestamp.isAfter(cutoffDate)).toList();

      final eventsJson =
          jsonEncode(filteredEvents.map((e) => e.toJson()).toList());
      await _prefs.setString(_auditLogKey, eventsJson);
    } catch (e) {
      debugPrint('Error storing audit event locally: $e');
    }
  }

  /// Send audit event to remote service
  Future<void> _sendAuditEventToRemote(AuditEvent event) async {
    try {
      await _supabaseClient.from('security_audit_log').insert(event.toJson());
    } catch (e) {
      debugPrint('Error sending audit event to remote: $e');
      // Store for retry later
      await _storeFailedAuditEvent(event);
    }
  }

  /// Handle critical security events
  Future<void> _handleCriticalEvent(AuditEvent event) async {
    // Log critical event
    debugPrint('CRITICAL SECURITY EVENT: ${event.description}');

    // Send immediate notification to security team
    await _sendSecurityAlert(event);

    // Consider triggering emergency lockdown if needed
    if (_shouldTriggerEmergencyLockdown(event)) {
      await _triggerEmergencyLockdown(event);
    }
  }

  /// Send security alert
  Future<void> _sendSecurityAlert(AuditEvent event) async {
    try {
      // This would integrate with your notification system
      // For now, just log the alert
      debugPrint('SECURITY ALERT: ${event.eventType} - ${event.description}');
    } catch (e) {
      debugPrint('Error sending security alert: $e');
    }
  }

  /// Check if emergency lockdown should be triggered
  bool _shouldTriggerEmergencyLockdown(AuditEvent event) {
    const emergencyEvents = [
      'multiple_failed_logins',
      'suspicious_transaction_pattern',
      'potential_account_takeover',
      'data_breach_detected',
      'malicious_activity_detected',
    ];

    return emergencyEvents.contains(event.eventType);
  }

  /// Trigger emergency lockdown
  Future<void> _triggerEmergencyLockdown(AuditEvent event) async {
    try {
      // Lock user account
      await _supabaseClient.from('users').update({
        'locked_until': DateTime.now()
            .add(SecurityConfig.emergencyLockoutDuration)
            .toIso8601String()
      }).eq('id', event.userId);

      // Log lockdown event
      await logAuditEvent(
        eventType: 'emergency_lockdown',
        userId: event.userId,
        description:
            'Emergency lockdown triggered due to: ${event.description}',
        severity: 'critical',
        metadata: {'trigger_event_id': event.id},
      );
    } catch (e) {
      debugPrint('Error triggering emergency lockdown: $e');
    }
  }

  /// Get local audit events
  Future<List<AuditEvent>> _getLocalAuditEvents() async {
    try {
      final eventsJson = _prefs.getString(_auditLogKey);
      if (eventsJson == null) return [];

      final List<dynamic> eventsList = jsonDecode(eventsJson);
      return eventsList.map((json) => AuditEvent.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error getting local audit events: $e');
      return [];
    }
  }

  /// Store failed audit event for retry
  Future<void> _storeFailedAuditEvent(AuditEvent event) async {
    try {
      final failedEvents = _prefs.getStringList('failed_audit_events') ?? [];
      failedEvents.add(jsonEncode(event.toJson()));
      await _prefs.setStringList('failed_audit_events', failedEvents);
    } catch (e) {
      debugPrint('Error storing failed audit event: $e');
    }
  }

  /// Retry failed audit events
  Future<void> retryFailedAuditEvents() async {
    try {
      final failedEvents = _prefs.getStringList('failed_audit_events') ?? [];
      final successfulEvents = <String>[];

      for (final eventJson in failedEvents) {
        try {
          final eventData = jsonDecode(eventJson);
          await _supabaseClient.from('security_audit_log').insert(eventData);
          successfulEvents.add(eventJson);
        } catch (e) {
          debugPrint('Failed to retry audit event: $e');
        }
      }

      // Remove successful events
      failedEvents.removeWhere((event) => successfulEvents.contains(event));
      await _prefs.setStringList('failed_audit_events', failedEvents);
    } catch (e) {
      debugPrint('Error retrying failed audit events: $e');
    }
  }

  /// Perform security compliance assessment
  Future<ComplianceStatus> performComplianceAssessment() async {
    final violations = <String>[];
    final recommendations = <String>[];

    // Check PCI compliance
    bool pciCompliant = true;
    if (!SecurityConfig.pciComplianceMode) {
      pciCompliant = false;
      violations.add('PCI compliance mode not enabled');
    }

    // Check GDPR compliance
    bool gdprCompliant = true;
    if (!SecurityConfig.gdprComplianceMode) {
      gdprCompliant = false;
      violations.add('GDPR compliance mode not enabled');
    }

    // Check CCPA compliance
    bool ccpaCompliant = true;
    if (!SecurityConfig.ccpaComplianceMode) {
      ccpaCompliant = false;
      violations.add('CCPA compliance mode not enabled');
    }

    // Check encryption settings
    if (!SecurityConfig.requireTLS) {
      violations.add('TLS not required for all connections');
      recommendations.add('Enable TLS requirement');
    }

    // Check certificate pinning
    if (!SecurityConfig.certificatePinningEnabled) {
      recommendations.add('Enable certificate pinning for enhanced security');
    }

    // Check biometric settings
    if (!SecurityConfig.biometricFallbackEnabled) {
      recommendations
          .add('Enable biometric fallback for better user experience');
    }

    final status = ComplianceStatus(
      pciCompliant: pciCompliant,
      gdprCompliant: gdprCompliant,
      ccpaCompliant: ccpaCompliant,
      lastAssessment: DateTime.now(),
      violations: violations,
      recommendations: recommendations,
    );

    // Store compliance status
    await _storeComplianceStatus(status);

    return status;
  }

  /// Store compliance status
  Future<void> _storeComplianceStatus(ComplianceStatus status) async {
    try {
      final statusJson = jsonEncode(status.toJson());
      await _prefs.setString(_complianceStatusKey, statusJson);
    } catch (e) {
      debugPrint('Error storing compliance status: $e');
    }
  }

  /// Get stored compliance status
  Future<ComplianceStatus?> getComplianceStatus() async {
    try {
      final statusJson = _prefs.getString(_complianceStatusKey);
      if (statusJson == null) return null;

      final statusData = jsonDecode(statusJson);
      return ComplianceStatus.fromJson(statusData);
    } catch (e) {
      debugPrint('Error getting compliance status: $e');
      return null;
    }
  }

  /// Generate security report
  Future<Map<String, dynamic>> generateSecurityReport() async {
    final events = await _getLocalAuditEvents();
    final complianceStatus = await getComplianceStatus();

    // Analyze events by severity
    final eventsBySeverity = <String, int>{};
    for (final event in events) {
      eventsBySeverity[event.severity] =
          (eventsBySeverity[event.severity] ?? 0) + 1;
    }

    // Analyze events by type
    final eventsByType = <String, int>{};
    for (final event in events) {
      eventsByType[event.eventType] = (eventsByType[event.eventType] ?? 0) + 1;
    }

    return {
      'report_generated': DateTime.now().toIso8601String(),
      'total_events': events.length,
      'events_by_severity': eventsBySeverity,
      'events_by_type': eventsByType,
      'compliance_status': complianceStatus?.toJson(),
      'recent_critical_events': events
          .where((e) => e.severity == 'critical')
          .take(10)
          .map((e) => e.toJson())
          .toList(),
    };
  }

  /// Clear old audit data
  Future<void> clearOldAuditData() async {
    try {
      final events = await _getLocalAuditEvents();
      final cutoffDate =
          DateTime.now().subtract(SecurityConfig.auditLogRetention);

      final recentEvents =
          events.where((e) => e.timestamp.isAfter(cutoffDate)).toList();

      final eventsJson =
          jsonEncode(recentEvents.map((e) => e.toJson()).toList());
      await _prefs.setString(_auditLogKey, eventsJson);
    } catch (e) {
      debugPrint('Error clearing old audit data: $e');
    }
  }
}
