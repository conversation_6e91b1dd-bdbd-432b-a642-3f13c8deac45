import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../config/app_config.dart';
import '../errors/exceptions.dart';
import '../services/security_service.dart';

class DioClient {
  static Dio createDio() {
    final dio = Dio();

    // Base options with security headers
    dio.options = BaseOptions(
      baseUrl: AppConfig.isProduction
          ? 'https://api.potto.app'
          : 'https://api-staging.potto.app',
      connectTimeout: AppConfig.apiTimeout,
      receiveTimeout: AppConfig.apiTimeout,
      sendTimeout: AppConfig.apiTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
      validateStatus: (status) {
        // Accept status codes 200-299 and 304 (not modified)
        return status != null &&
            ((status >= 200 && status < 300) || status == 304);
      },
    );

    // Add security interceptors
    dio.interceptors.add(_createSecurityInterceptor());
    dio.interceptors.add(_createLogInterceptor());
    dio.interceptors.add(_createErrorInterceptor());
    dio.interceptors.add(_createAuthInterceptor());

    return dio;
  }

  static InterceptorsWrapper _createSecurityInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add security headers
        options.headers['X-App-Version'] = AppConfig.appVersion;
        options.headers['X-Platform'] = 'flutter';

        // Add request timestamp for replay attack prevention
        options.headers['X-Timestamp'] =
            DateTime.now().millisecondsSinceEpoch.toString();

        // Validate request data for security threats
        if (options.data is Map<String, dynamic>) {
          final data = options.data as Map<String, dynamic>;
          for (final entry in data.entries) {
            if (entry.value is String) {
              if (!SecurityService.isInputSafe(entry.value)) {
                handler.reject(
                  DioException(
                    requestOptions: options,
                    error: 'Invalid request data detected',
                    type: DioExceptionType.badResponse,
                  ),
                );
                return;
              }
            }
          }
        }

        handler.next(options);
      },
      onResponse: (response, handler) {
        // Validate response headers for security
        final contentType = response.headers.value('content-type');
        if (contentType != null && !contentType.contains('application/json')) {
          // Log suspicious response type
          if (kDebugMode) {
            print('[SECURITY] Unexpected content type: $contentType');
          }
        }

        handler.next(response);
      },
      onError: (error, handler) {
        // Log security-related errors
        if (error.response?.statusCode == 403) {
          if (kDebugMode) {
            print('[SECURITY] Access forbidden - possible security violation');
          }
        }

        handler.next(error);
      },
    );
  }

  static LogInterceptor _createLogInterceptor() {
    return LogInterceptor(
      request: kDebugMode,
      requestHeader: kDebugMode,
      requestBody: kDebugMode,
      responseHeader: kDebugMode,
      responseBody: kDebugMode,
      error: kDebugMode,
      logPrint: (object) {
        if (kDebugMode) {
          print('[DIO] $object');
        }
      },
    );
  }

  static InterceptorsWrapper _createErrorInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) {
        final exception = _handleDioError(error);
        handler.reject(
          DioException(
            requestOptions: error.requestOptions,
            error: exception,
            type: error.type,
            response: error.response,
          ),
        );
      },
    );
  }

  static InterceptorsWrapper _createAuthInterceptor() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        try {
          // Get auth token from Supabase
          final supabase = Supabase.instance.client;
          final session = supabase.auth.currentSession;

          if (session?.accessToken != null) {
            options.headers['Authorization'] = 'Bearer ${session!.accessToken}';
          }

          // Add additional security headers for authenticated requests
          if (session != null) {
            options.headers['X-User-ID'] = session.user.id;
            options.headers['X-Session-ID'] =
                session.accessToken.substring(0, 8);
          }
        } catch (e) {
          if (kDebugMode) {
            print('[AUTH] Error adding auth headers: $e');
          }
        }

        handler.next(options);
      },
      onResponse: (response, handler) {
        handler.next(response);
      },
      onError: (error, handler) {
        if (error.response?.statusCode == 401) {
          // Handle token refresh or logout
          if (kDebugMode) {
            print('[AUTH] Unauthorized request - token may be expired');
          }

          // Trigger auth state change to handle logout
          try {
            final supabase = Supabase.instance.client;
            supabase.auth.signOut();
          } catch (e) {
            if (kDebugMode) {
              print('[AUTH] Error during logout: $e');
            }
          }
        }
        handler.next(error);
      },
    );
  }

  static AppException _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const TimeoutException();

      case DioExceptionType.badResponse:
        return _handleResponseError(error.response);

      case DioExceptionType.cancel:
        return const AppException(
          message: 'Request cancelled',
          code: 499,
        );

      case DioExceptionType.connectionError:
        return const NetworkException(
          message: 'Network connection error',
          code: 0,
        );

      case DioExceptionType.badCertificate:
        return const NetworkException(
          message: 'Certificate verification failed',
          code: 0,
        );

      case DioExceptionType.unknown:
        return AppException(
          message: error.message ?? 'Unknown error occurred',
          code: 0,
        );
    }
  }

  static AppException _handleResponseError(Response? response) {
    if (response == null) {
      return const ServerException(
        message: 'No response from server',
        code: 0,
      );
    }

    final statusCode = response.statusCode ?? 0;
    final data = response.data;

    String message = 'Server error';
    if (data is Map<String, dynamic> && data.containsKey('message')) {
      message = data['message'] as String;
    } else if (data is Map<String, dynamic> && data.containsKey('error')) {
      message = data['error'] as String;
    }

    switch (statusCode) {
      case 400:
        return ValidationException(
          message: message,
          code: statusCode,
          data: data,
        );

      case 401:
        return UnauthorizedException(
          message: message,
          code: statusCode,
          data: data,
        );

      case 403:
        return ForbiddenException(
          message: message,
          code: statusCode,
          data: data,
        );

      case 404:
        return ServerException(
          message: 'Resource not found',
          code: statusCode,
          data: data,
        );

      case 422:
        return ValidationException(
          message: message,
          code: statusCode,
          data: data,
        );

      case 429:
        return ServerException(
          message: 'Too many requests',
          code: statusCode,
          data: data,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return ServerException(
          message: 'Server error',
          code: statusCode,
          data: data,
        );

      default:
        return ServerException(
          message: message,
          code: statusCode,
          data: data,
        );
    }
  }
}
