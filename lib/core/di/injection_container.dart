// TODO: Add get_it dependency to pubspec.yaml
// import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import '../config/app_config.dart';
import '../network/api_client.dart';
import '../network/dio_client.dart';
import '../services/notification_service.dart';
import '../services/realtime_service.dart';
import '../services/chat_service.dart';
import '../services/storage_service.dart';
import '../services/stripe_service.dart';
import '../services/stripe_issuing_service.dart';
import '../services/webhook_service.dart';
import '../services/security_service.dart';
import '../services/fraud_prevention_service.dart';

// TODO: Uncomment when missing files are created
// Data Layer
// import '../../data/datasources/auth_remote_datasource.dart';
// import '../../data/datasources/wallet_remote_datasource.dart';
// import '../../data/datasources/wallet_remote_datasource_impl.dart';
// import '../../data/datasources/payment_remote_datasource.dart';
// import '../../data/datasources/card_remote_datasource.dart';
// import '../../data/datasources/virtual_card_remote_datasource.dart';
// import '../../data/repositories/auth_repository_impl.dart';
// import '../../data/repositories/wallet_repository_impl.dart';
// import '../../data/repositories/payment_repository_impl.dart';
// import '../../data/repositories/card_repository_impl.dart';
// import '../../data/repositories/virtual_card_repository_impl.dart';

// Domain Layer
// import '../../domain/repositories/auth_repository.dart';
// import '../../domain/repositories/wallet_repository.dart';
// import '../../domain/repositories/payment_repository.dart';
// import '../../domain/repositories/card_repository.dart';
// import '../../domain/repositories/virtual_card_repository.dart';
// import '../../domain/usecases/auth/login_usecase.dart';
// import '../../domain/usecases/auth/register_usecase.dart';
// import '../../domain/usecases/auth/logout_usecase.dart';
// import '../../domain/usecases/wallet/create_wallet_usecase.dart';
// import '../../domain/usecases/wallet/join_wallet_usecase.dart';
// import '../../domain/usecases/wallet/get_wallets_usecase.dart';
// import '../../domain/usecases/wallet/invitation_usecase.dart';
// import '../../domain/usecases/wallet/member_management_usecase.dart';
// import '../../domain/usecases/payment/create_payment_intent_usecase.dart';
// import '../../domain/usecases/payment/process_payment_usecase.dart';
// import '../../domain/usecases/payment/create_customer_usecase.dart';
// import '../../domain/usecases/card/create_virtual_card_usecase.dart';
// import '../../domain/usecases/card/get_cards_usecase.dart';
// import '../../domain/usecases/virtual_card/create_virtual_card_usecase.dart';
// import '../../domain/usecases/virtual_card/get_virtual_cards_usecase.dart';
// import '../../domain/usecases/virtual_card/manage_virtual_card_usecase.dart';
// import '../../domain/usecases/virtual_card/spending_control_usecase.dart';

// Presentation Layer
// import '../../presentation/blocs/auth/auth_bloc.dart';
// import '../../presentation/blocs/app/app_bloc.dart';
// import '../../presentation/blocs/wallet/wallet_bloc.dart';
// import '../../presentation/blocs/payment/payment_bloc.dart';
// import '../../presentation/blocs/card/card_bloc.dart';
// import '../../presentation/blocs/virtual_card/virtual_card_bloc.dart';
// import '../../presentation/blocs/realtime/realtime_bloc.dart';

// TODO: Uncomment when get_it is added to dependencies
// final GetIt getIt = GetIt.instance;

Future<void> initializeDependencies() async {
  // TODO: Implement dependency injection when get_it is added to pubspec.yaml
  // and missing files are created
  /*
  // External Dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  final supabaseClient = Supabase.instance.client;
  getIt.registerSingleton<SupabaseClient>(supabaseClient);

  // Core Services
  getIt.registerSingleton<StorageService>(StorageService(sharedPreferences));

  // Initialize security service
  await SecurityService.initialize();

  getIt.registerSingleton<FraudPreventionService>(
    FraudPreventionService(sharedPreferences),
  );

  getIt.registerSingleton<SecurityAuditService>(
    SecurityAuditService(sharedPreferences, supabaseClient),
  );

  getIt.registerSingleton<NotificationService>(NotificationService());

  getIt.registerSingleton<StripeService>(StripeService(getIt<Dio>()));

  getIt.registerSingleton<StripeIssuingService>(
    StripeIssuingService(getIt<Dio>()),
  );

  getIt.registerSingleton<WebhookService>(
    WebhookService(getIt<SupabaseClient>()),
  );

  getIt.registerSingleton<RealtimeService>(
    RealtimeService(getIt<SupabaseClient>()),
  );

  getIt.registerSingleton<ChatService>(
    ChatService(getIt<SupabaseClient>(), const Uuid()),
  );

  // Network
  getIt.registerSingleton<Dio>(DioClient.createDio());

  getIt.registerSingleton<ApiClient>(ApiClient(getIt<Dio>()));

  // Data Sources
  getIt.registerSingleton<AuthRemoteDataSource>(
    AuthRemoteDataSourceImpl(getIt<SupabaseClient>()),
  );

  getIt.registerSingleton<WalletRemoteDataSource>(
    WalletRemoteDataSourceImpl(
      supabaseClient: getIt<SupabaseClient>(),
      uuid: const Uuid(),
    ),
  );

  getIt.registerSingleton<PaymentRemoteDataSource>(
    PaymentRemoteDataSourceImpl(getIt<StripeService>()),
  );

  getIt.registerSingleton<CardRemoteDataSource>(
    CardRemoteDataSourceImpl(getIt<ApiClient>()),
  );

  getIt.registerSingleton<VirtualCardRemoteDataSource>(
    VirtualCardRemoteDataSourceImpl(
      supabaseClient: getIt<SupabaseClient>(),
      stripeIssuingService: getIt<StripeIssuingService>(),
    ),
  );

  // Repositories
  getIt.registerSingleton<AuthRepository>(
    AuthRepositoryImpl(
      remoteDataSource: getIt<AuthRemoteDataSource>(),
      storageService: getIt<StorageService>(),
    ),
  );

  getIt.registerSingleton<WalletRepository>(
    WalletRepositoryImpl(remoteDataSource: getIt<WalletRemoteDataSource>()),
  );

  getIt.registerSingleton<PaymentRepository>(
    PaymentRepositoryImpl(getIt<PaymentRemoteDataSource>()),
  );

  getIt.registerSingleton<CardRepository>(
    CardRepositoryImpl(remoteDataSource: getIt<CardRemoteDataSource>()),
  );

  getIt.registerSingleton<VirtualCardRepository>(
    VirtualCardRepositoryImpl(
      remoteDataSource: getIt<VirtualCardRemoteDataSource>(),
    ),
  );

  // Use Cases
  _registerUseCases();

  // Blocs
  _registerBlocs();
}

void _registerUseCases() {
  // Auth Use Cases
  getIt.registerFactory(() => LoginUseCase(getIt<AuthRepository>()));
  getIt.registerFactory(() => RegisterUseCase(getIt<AuthRepository>()));
  getIt.registerFactory(() => LogoutUseCase(getIt<AuthRepository>()));

  // Wallet Use Cases
  getIt.registerFactory(() => CreateWalletUseCase(getIt<WalletRepository>()));
  getIt.registerFactory(() => JoinWalletUseCase(getIt<WalletRepository>()));
  getIt.registerFactory(() => GetWalletsUseCase(getIt<WalletRepository>()));
  getIt.registerFactory(() => GetWalletUseCase(getIt<WalletRepository>()));

  // Invitation Use Cases
  getIt.registerFactory(
    () => CreateInvitationUseCase(getIt<WalletRepository>()),
  );
  getIt.registerFactory(() => GetInvitationsUseCase(getIt<WalletRepository>()));
  getIt.registerFactory(
    () => AcceptInvitationUseCase(getIt<WalletRepository>()),
  );
  getIt.registerFactory(
    () => CancelInvitationUseCase(getIt<WalletRepository>()),
  );

  // Member Management Use Cases
  getIt.registerFactory(
    () => UpdateMemberRoleUseCase(getIt<WalletRepository>()),
  );
  getIt.registerFactory(
    () => UpdateMemberPermissionsUseCase(getIt<WalletRepository>()),
  );
  getIt.registerFactory(() => RemoveMemberUseCase(getIt<WalletRepository>()));
  getIt.registerFactory(() => GetMembersUseCase(getIt<WalletRepository>()));

  // Payment Use Cases
  getIt.registerFactory(
    () => CreatePaymentIntentUseCase(getIt<PaymentRepository>()),
  );
  getIt.registerFactory(
    () => ProcessPaymentUseCase(getIt<PaymentRepository>()),
  );
  getIt.registerFactory(
    () => CreateCustomerUseCase(getIt<PaymentRepository>()),
  );

  // Card Use Cases
  getIt.registerFactory(
    () => CreateVirtualCardUseCase(getIt<CardRepository>()),
  );
  getIt.registerFactory(() => GetCardsUseCase(getIt<CardRepository>()));

  // Virtual Card Use Cases
  getIt.registerFactory(
    () => CreateVirtualCardUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => GetVirtualCardsByWalletUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => GetVirtualCardsByUserUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => GetVirtualCardUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => UpdateVirtualCardUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => DeleteVirtualCardUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => ActivateCardUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => DeactivateCardUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => CancelCardUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => CreateSpendingControlUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => GetSpendingControlsUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => UpdateSpendingControlUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => DeleteSpendingControlUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => GetCardDetailsUseCase(getIt<VirtualCardRepository>()),
  );
  getIt.registerFactory(
    () => AuthorizeTransactionUseCase(getIt<VirtualCardRepository>()),
  );
}

void _registerBlocs() {
  getIt.registerFactory(() => AppBloc(getIt<StorageService>()));

  getIt.registerFactory(
    () => AuthBloc(
      loginUseCase: getIt<LoginUseCase>(),
      registerUseCase: getIt<RegisterUseCase>(),
      logoutUseCase: getIt<LogoutUseCase>(),
    ),
  );

  getIt.registerFactory(
    () => WalletBloc(
      createWalletUseCase: getIt<CreateWalletUseCase>(),
      joinWalletUseCase: getIt<JoinWalletUseCase>(),
      getWalletsUseCase: getIt<GetWalletsUseCase>(),
      getWalletUseCase: getIt<GetWalletUseCase>(),
      createInvitationUseCase: getIt<CreateInvitationUseCase>(),
      getInvitationsUseCase: getIt<GetInvitationsUseCase>(),
      acceptInvitationUseCase: getIt<AcceptInvitationUseCase>(),
      cancelInvitationUseCase: getIt<CancelInvitationUseCase>(),
      updateMemberRoleUseCase: getIt<UpdateMemberRoleUseCase>(),
      updateMemberPermissionsUseCase: getIt<UpdateMemberPermissionsUseCase>(),
      removeMemberUseCase: getIt<RemoveMemberUseCase>(),
      getMembersUseCase: getIt<GetMembersUseCase>(),
    ),
  );

  getIt.registerFactory(
    () => PaymentBloc(
      getIt<CreatePaymentIntentUseCase>(),
      getIt<ProcessPaymentUseCase>(),
      getIt<CreateCustomerUseCase>(),
    ),
  );

  getIt.registerFactory(
    () => CardBloc(
      createVirtualCardUseCase: getIt<CreateVirtualCardUseCase>(),
      getCardsUseCase: getIt<GetCardsUseCase>(),
    ),
  );

  getIt.registerFactory(
    () => VirtualCardBloc(
      createVirtualCardUseCase: getIt<CreateVirtualCardUseCase>(),
      getVirtualCardsByWalletUseCase: getIt<GetVirtualCardsByWalletUseCase>(),
      getVirtualCardsByUserUseCase: getIt<GetVirtualCardsByUserUseCase>(),
      getVirtualCardUseCase: getIt<GetVirtualCardUseCase>(),
      updateVirtualCardUseCase: getIt<UpdateVirtualCardUseCase>(),
      deleteVirtualCardUseCase: getIt<DeleteVirtualCardUseCase>(),
      activateCardUseCase: getIt<ActivateCardUseCase>(),
      deactivateCardUseCase: getIt<DeactivateCardUseCase>(),
      cancelCardUseCase: getIt<CancelCardUseCase>(),
      createSpendingControlUseCase: getIt<CreateSpendingControlUseCase>(),
      getSpendingControlsUseCase: getIt<GetSpendingControlsUseCase>(),
      updateSpendingControlUseCase: getIt<UpdateSpendingControlUseCase>(),
      deleteSpendingControlUseCase: getIt<DeleteSpendingControlUseCase>(),
      getCardDetailsUseCase: getIt<GetCardDetailsUseCase>(),
      authorizeTransactionUseCase: getIt<AuthorizeTransactionUseCase>(),
    ),
  );

  getIt.registerFactory(
    () => RealtimeBloc(
      realtimeService: getIt<RealtimeService>(),
      chatService: getIt<ChatService>(),
    ),
  );
  */
}
