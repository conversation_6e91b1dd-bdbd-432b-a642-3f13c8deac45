class AppException implements Exception {
  final String message;
  final int? code;
  final dynamic data;

  const AppException({
    required this.message,
    this.code,
    this.data,
  });

  @override
  String toString() => 'AppException: $message (Code: $code)';
}

// Server exceptions
class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.code,
    super.data,
  });
}

class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code,
    super.data,
  });
}

class TimeoutException extends AppException {
  const TimeoutException({
    super.message = 'Request timeout',
    super.code = 408,
    super.data,
  });
}

// Cache exceptions
class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.code,
    super.data,
  });
}

// Database exceptions
class DatabaseException extends AppException {
  const DatabaseException({
    required super.message,
    super.code,
    super.data,
  });
}

// Authentication exceptions
class AuthException extends AppException {
  const AuthException({
    required super.message,
    super.code,
    super.data,
  });
}

class UnauthorizedException extends AuthException {
  const UnauthorizedException({
    super.message = 'Unauthorized',
    super.code = 401,
    super.data,
  });
}

class ForbiddenException extends AuthException {
  const ForbiddenException({
    super.message = 'Forbidden',
    super.code = 403,
    super.data,
  });
}

class TokenExpiredException extends AuthException {
  const TokenExpiredException({
    super.message = 'Token expired',
    super.code = 401,
    super.data,
  });
}

// Validation exceptions
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code = 400,
    super.data,
  });
}

// Payment exceptions
class PaymentException extends AppException {
  const PaymentException({
    required super.message,
    super.code,
    super.data,
  });
}

class StripeException extends PaymentException {
  const StripeException({
    required super.message,
    super.code,
    super.data,
  });
}

// Wallet exceptions
class WalletException extends AppException {
  const WalletException({
    required super.message,
    super.code,
    super.data,
  });
}

// Card exceptions
class CardException extends AppException {
  const CardException({
    required super.message,
    super.code,
    super.data,
  });
}
