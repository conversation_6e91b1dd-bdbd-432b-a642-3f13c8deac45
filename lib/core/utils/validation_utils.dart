import 'package:flutter/services.dart';

/// Comprehensive input validation utilities
class ValidationUtils {
  // Email validation regex
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  // Phone number validation regex (international format)
  static final RegExp _phoneRegex = RegExp(
    r'^\+?[1-9]\d{1,14}$',
  );

  // Strong password regex
  static final RegExp _strongPasswordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
  );

  // Credit card number regex (basic validation)
  static final RegExp _creditCardRegex = RegExp(
    r'^[0-9]{13,19}$',
  );

  // SQL injection patterns
  static final List<RegExp> _sqlInjectionPatterns = [
    RegExp(r'(union|select|insert|update|delete|drop|create|alter)\s+',
        caseSensitive: false),
    RegExp('[\'";].*(-{2}|/\\*|\\*/)', caseSensitive: false),
    RegExp(r'(exec|execute|sp_|xp_)', caseSensitive: false),
    RegExp(r'(script|javascript|vbscript)', caseSensitive: false),
  ];

  // XSS patterns
  static final List<RegExp> _xssPatterns = [
    RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false),
    RegExp(r'javascript:', caseSensitive: false),
    RegExp(r'on\w+\s*=', caseSensitive: false),
    RegExp(r'<iframe[^>]*>.*?</iframe>', caseSensitive: false),
    RegExp(r'<object[^>]*>.*?</object>', caseSensitive: false),
  ];

  /// Validate email address
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'Email is required';
    }

    if (!_emailRegex.hasMatch(email)) {
      return 'Please enter a valid email address';
    }

    if (email.length > 254) {
      return 'Email address is too long';
    }

    return null;
  }

  /// Validate password
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }

    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }

    if (password.length > 128) {
      return 'Password is too long';
    }

    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }

    if (!password.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }

    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }

    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return 'Password must contain at least one special character';
    }

    return null;
  }

  /// Validate phone number
  static String? validatePhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty) {
      return 'Phone number is required';
    }

    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    if (!_phoneRegex.hasMatch(cleanPhone)) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validate name (first name, last name)
  static String? validateName(String? name, {String fieldName = 'Name'}) {
    if (name == null || name.isEmpty) {
      return '$fieldName is required';
    }

    if (name.length < 2) {
      return '$fieldName must be at least 2 characters long';
    }

    if (name.length > 50) {
      return '$fieldName is too long';
    }

    if (!RegExp(r"^[a-zA-Z\s\-\.']+$").hasMatch(name)) {
      return '$fieldName contains invalid characters';
    }

    return null;
  }

  /// Validate amount
  static String? validateAmount(
    String? amount, {
    double? minAmount,
    double? maxAmount,
    String fieldName = 'Amount',
  }) {
    if (amount == null || amount.isEmpty) {
      return '$fieldName is required';
    }

    final parsedAmount = double.tryParse(amount);
    if (parsedAmount == null) {
      return 'Please enter a valid $fieldName';
    }

    if (parsedAmount <= 0) {
      return '$fieldName must be greater than zero';
    }

    if (minAmount != null && parsedAmount < minAmount) {
      return '$fieldName must be at least \$${minAmount.toStringAsFixed(2)}';
    }

    if (maxAmount != null && parsedAmount > maxAmount) {
      return '$fieldName cannot exceed \$${maxAmount.toStringAsFixed(2)}';
    }

    return null;
  }

  /// Validate wallet name
  static String? validateWalletName(String? name) {
    if (name == null || name.isEmpty) {
      return 'Wallet name is required';
    }

    if (name.length < 3) {
      return 'Wallet name must be at least 3 characters long';
    }

    if (name.length > 50) {
      return 'Wallet name is too long';
    }

    if (!RegExp(r'^[a-zA-Z0-9\s\-_]+$').hasMatch(name)) {
      return 'Wallet name contains invalid characters';
    }

    return null;
  }

  /// Validate description
  static String? validateDescription(
    String? description, {
    bool required = false,
    int maxLength = 200,
  }) {
    if (required && (description == null || description.isEmpty)) {
      return 'Description is required';
    }

    if (description != null && description.length > maxLength) {
      return 'Description is too long (max $maxLength characters)';
    }

    return null;
  }

  /// Validate PIN
  static String? validatePIN(String? pin) {
    if (pin == null || pin.isEmpty) {
      return 'PIN is required';
    }

    if (pin.length != 4 && pin.length != 6) {
      return 'PIN must be 4 or 6 digits';
    }

    if (!RegExp(r'^[0-9]+$').hasMatch(pin)) {
      return 'PIN must contain only numbers';
    }

    // Check for weak PINs
    if (pin == '0000' || pin == '1234' || pin == '1111' || pin == '2222') {
      return 'Please choose a more secure PIN';
    }

    return null;
  }

  /// Validate credit card number (basic Luhn algorithm)
  static String? validateCreditCard(String? cardNumber) {
    if (cardNumber == null || cardNumber.isEmpty) {
      return 'Card number is required';
    }

    final cleanNumber = cardNumber.replaceAll(RegExp(r'\s'), '');

    if (!_creditCardRegex.hasMatch(cleanNumber)) {
      return 'Please enter a valid card number';
    }

    if (!_isValidLuhn(cleanNumber)) {
      return 'Invalid card number';
    }

    return null;
  }

  /// Validate CVV
  static String? validateCVV(String? cvv) {
    if (cvv == null || cvv.isEmpty) {
      return 'CVV is required';
    }

    if (!RegExp(r'^[0-9]{3,4}$').hasMatch(cvv)) {
      return 'CVV must be 3 or 4 digits';
    }

    return null;
  }

  /// Validate expiry date (MM/YY format)
  static String? validateExpiryDate(String? expiry) {
    if (expiry == null || expiry.isEmpty) {
      return 'Expiry date is required';
    }

    if (!RegExp(r'^(0[1-9]|1[0-2])\/([0-9]{2})$').hasMatch(expiry)) {
      return 'Please enter date in MM/YY format';
    }

    final parts = expiry.split('/');
    final month = int.parse(parts[0]);
    final year = int.parse('20${parts[1]}');
    final now = DateTime.now();
    final expiryDate = DateTime(year, month + 1, 0);

    if (expiryDate.isBefore(now)) {
      return 'Card has expired';
    }

    return null;
  }

  /// Check for SQL injection attempts
  static bool containsSQLInjection(String input) {
    for (final pattern in _sqlInjectionPatterns) {
      if (pattern.hasMatch(input)) {
        return true;
      }
    }
    return false;
  }

  /// Check for XSS attempts
  static bool containsXSS(String input) {
    for (final pattern in _xssPatterns) {
      if (pattern.hasMatch(input)) {
        return true;
      }
    }
    return false;
  }

  /// Sanitize input to prevent injection attacks
  static String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll(RegExp('[<>"' "' ]"), '') // Remove dangerous characters
        .replaceAll(
            RegExp(r'(javascript|script|eval|expression)',
                caseSensitive: false),
            '')
        .trim();
  }

  /// Validate input for security threats
  static String? validateSecureInput(String? input,
      {String fieldName = 'Input'}) {
    if (input == null || input.isEmpty) {
      return '$fieldName is required';
    }

    if (containsSQLInjection(input)) {
      return '$fieldName contains invalid characters';
    }

    if (containsXSS(input)) {
      return '$fieldName contains invalid characters';
    }

    return null;
  }

  /// Luhn algorithm for credit card validation
  static bool _isValidLuhn(String cardNumber) {
    int sum = 0;
    bool alternate = false;

    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);

      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }

      sum += digit;
      alternate = !alternate;
    }

    return sum % 10 == 0;
  }

  /// Get input formatters for secure text input
  static List<TextInputFormatter> getSecureTextFormatters({
    int? maxLength,
    bool allowNumbers = true,
    bool allowLetters = true,
    bool allowSpecialChars = false,
  }) {
    final formatters = <TextInputFormatter>[];

    if (maxLength != null) {
      formatters.add(LengthLimitingTextInputFormatter(maxLength));
    }

    // Create allowed characters pattern
    String pattern = '';
    if (allowLetters) pattern += 'a-zA-Z';
    if (allowNumbers) pattern += '0-9';
    if (allowSpecialChars) pattern += r'\s\-_\.@';

    if (pattern.isNotEmpty) {
      formatters.add(FilteringTextInputFormatter.allow(RegExp('[$pattern]')));
    }

    return formatters;
  }

  /// Get input formatters for amount input
  static List<TextInputFormatter> getAmountFormatters({int decimalPlaces = 2}) {
    return [
      FilteringTextInputFormatter.allow(
          RegExp(r'^\d*\.?\d{0,' '$decimalPlaces' '}')),
      LengthLimitingTextInputFormatter(10),
    ];
  }

  /// Get input formatters for phone number
  static List<TextInputFormatter> getPhoneFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s\(\)]')),
      LengthLimitingTextInputFormatter(20),
    ];
  }

  /// Get input formatters for credit card
  static List<TextInputFormatter> getCreditCardFormatters() {
    return [
      FilteringTextInputFormatter.digitsOnly,
      LengthLimitingTextInputFormatter(19),
      _CreditCardFormatter(),
    ];
  }
}

/// Custom formatter for credit card numbers
class _CreditCardFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll(RegExp(r'\s'), '');
    final buffer = StringBuffer();

    for (int i = 0; i < text.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write(' ');
      }
      buffer.write(text[i]);
    }

    final formatted = buffer.toString();
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }

  /// Validate credit card number using Luhn algorithm
  static bool isValidLuhn(String cardNumber) {
    if (cardNumber.isEmpty) return false;

    // Remove any spaces or dashes
    final cleanNumber = cardNumber.replaceAll(RegExp(r'[\s-]'), '');

    // Check if all characters are digits
    if (!RegExp(r'^\d+$').hasMatch(cleanNumber)) return false;

    // Apply Luhn algorithm
    int sum = 0;
    bool alternate = false;

    for (int i = cleanNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cleanNumber[i]);

      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }

      sum += digit;
      alternate = !alternate;
    }

    return sum % 10 == 0;
  }
}
