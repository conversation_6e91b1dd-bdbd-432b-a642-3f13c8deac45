import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';

import '../config/security_config.dart';
import '../services/security_service.dart';
import '../services/security_audit_service.dart';

/// Security middleware for app-wide security enforcement
class SecurityMiddleware {
  static final LocalAuthentication _localAuth = LocalAuthentication();
  static SecurityAuditService? _auditService;
  static bool _isInitialized = false;

  /// Initialize security middleware
  static Future<void> initialize(SecurityAuditService auditService) async {
    _auditService = auditService;
    await _performSecurityChecks();
    _isInitialized = true;
  }

  /// Perform initial security checks
  static Future<void> _performSecurityChecks() async {
    // Check device security
    await _checkDeviceSecurity();

    // Check app integrity
    await _checkAppIntegrity();

    // Check network security
    await _checkNetworkSecurity();
  }

  /// Check device security
  static Future<void> _checkDeviceSecurity() async {
    try {
      // Check if device is rooted/jailbroken
      if (SecurityConfig.detectJailbreak) {
        final isJailbroken = await _isDeviceJailbroken();
        if (isJailbroken) {
          await _auditService?.logAuditEvent(
            eventType: 'jailbroken_device_detected',
            userId: 'system',
            description: 'App running on jailbroken/rooted device',
            severity: 'high',
          );

          if (!kDebugMode) {
            throw SecurityException('Device security compromised');
          }
        }
      }

      // Check if device has screen lock
      if (SecurityConfig.requireDeviceLock) {
        final hasDeviceLock = await _hasDeviceLock();
        if (!hasDeviceLock) {
          await _auditService?.logAuditEvent(
            eventType: 'no_device_lock',
            userId: 'system',
            description: 'Device does not have screen lock enabled',
            severity: 'medium',
          );
        }
      }

      // Check for debugging
      if (SecurityConfig.detectDebugging && !kDebugMode) {
        final isDebugging = await _isDebuggingEnabled();
        if (isDebugging) {
          await _auditService?.logAuditEvent(
            eventType: 'debugging_detected',
            userId: 'system',
            description: 'Debugging detected on production app',
            severity: 'high',
          );

          throw SecurityException('Debugging not allowed in production');
        }
      }
    } catch (e) {
      debugPrint('Device security check failed: $e');
      if (e is SecurityException) rethrow;
    }
  }

  /// Check app integrity
  static Future<void> _checkAppIntegrity() async {
    try {
      // Check app signature (platform-specific implementation needed)
      if (SecurityConfig.requireAppSignature && !kDebugMode) {
        final isValidSignature = await _verifyAppSignature();
        if (!isValidSignature) {
          await _auditService?.logAuditEvent(
            eventType: 'invalid_app_signature',
            userId: 'system',
            description: 'App signature verification failed',
            severity: 'critical',
          );

          throw SecurityException('App integrity compromised');
        }
      }
    } catch (e) {
      debugPrint('App integrity check failed: $e');
      if (e is SecurityException) rethrow;
    }
  }

  /// Check network security
  static Future<void> _checkNetworkSecurity() async {
    try {
      // Verify TLS configuration
      if (SecurityConfig.requireTLS) {
        // This would be implemented with platform-specific code
        debugPrint('TLS requirement verified');
      }

      // Check certificate pinning configuration
      if (SecurityConfig.certificatePinningEnabled) {
        debugPrint('Certificate pinning enabled');
      }
    } catch (e) {
      debugPrint('Network security check failed: $e');
    }
  }

  /// Validate user input for security threats
  static String? validateInput(String input, {String? fieldName}) {
    if (SecurityConfig.containsSQLInjection(input)) {
      _auditService?.logAuditEvent(
        eventType: 'sql_injection_attempt',
        userId: 'unknown',
        description:
            'SQL injection attempt detected in ${fieldName ?? 'input'}',
        severity: 'high',
        metadata: {'input_field': fieldName, 'input_length': input.length},
      );
      return '${fieldName ?? 'Input'} contains invalid characters';
    }

    if (SecurityConfig.containsXSS(input)) {
      _auditService?.logAuditEvent(
        eventType: 'xss_attempt',
        userId: 'unknown',
        description: 'XSS attempt detected in ${fieldName ?? 'input'}',
        severity: 'high',
        metadata: {'input_field': fieldName, 'input_length': input.length},
      );
      return '${fieldName ?? 'Input'} contains invalid characters';
    }

    return null;
  }

  /// Sanitize user input
  static String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll(RegExp('[<>"' + "']"), '') // Remove dangerous characters
        .replaceAll(
            RegExp(r'(javascript|script|eval|expression)',
                caseSensitive: false),
            '')
        .trim();
  }

  /// Validate file upload
  static Future<bool> validateFileUpload(String filename, int fileSize) async {
    // Check file extension
    if (SecurityConfig.isDangerousFile(filename)) {
      await _auditService?.logAuditEvent(
        eventType: 'dangerous_file_upload',
        userId: 'unknown',
        description: 'Attempt to upload dangerous file: $filename',
        severity: 'high',
        metadata: {'filename': filename, 'file_size': fileSize},
      );
      return false;
    }

    // Check file size
    final maxSize = SecurityConfig.isAllowedImageFile(filename)
        ? SecurityConfig.maxImageSize
        : SecurityConfig.maxDocumentSize;

    if (fileSize > maxSize) {
      await _auditService?.logAuditEvent(
        eventType: 'oversized_file_upload',
        userId: 'unknown',
        description: 'Attempt to upload oversized file: $filename',
        severity: 'medium',
        metadata: {
          'filename': filename,
          'file_size': fileSize,
          'max_size': maxSize
        },
      );
      return false;
    }

    return true;
  }

  /// Enforce rate limiting
  static Future<bool> checkRateLimit(String action, String userId) async {
    // This would be implemented with a proper rate limiting service
    // For now, just log the action
    await _auditService?.logAuditEvent(
      eventType: 'rate_limit_check',
      userId: userId,
      description: 'Rate limit check for action: $action',
      severity: 'low',
      metadata: {'action': action},
    );

    return true; // Allow for now
  }

  /// Require biometric authentication
  static Future<bool> requireBiometricAuth({
    String reason = 'Please authenticate to continue',
  }) async {
    if (!await SecurityService.isBiometricAvailable()) {
      return false;
    }

    if (!await SecurityService.isBiometricEnabled()) {
      return false;
    }

    try {
      final authenticated = await SecurityService.authenticateWithBiometrics(
        localizedReason: reason,
      );

      await _auditService?.logAuditEvent(
        eventType: 'biometric_auth_attempt',
        userId: 'current_user',
        description: authenticated
            ? 'Biometric authentication successful'
            : 'Biometric authentication failed',
        severity: authenticated ? 'low' : 'medium',
        metadata: {'success': authenticated},
      );

      return authenticated;
    } catch (e) {
      await _auditService?.logAuditEvent(
        eventType: 'biometric_auth_error',
        userId: 'current_user',
        description: 'Biometric authentication error: $e',
        severity: 'medium',
        metadata: {'error': e.toString()},
      );
      return false;
    }
  }

  /// Check if device is jailbroken/rooted
  static Future<bool> _isDeviceJailbroken() async {
    if (Platform.isIOS) {
      return await _isIOSJailbroken();
    } else if (Platform.isAndroid) {
      return await _isAndroidRooted();
    }
    return false;
  }

  /// Check if iOS device is jailbroken
  static Future<bool> _isIOSJailbroken() async {
    try {
      // Check for common jailbreak files/directories
      final jailbreakPaths = [
        '/Applications/Cydia.app',
        '/Library/MobileSubstrate/MobileSubstrate.dylib',
        '/bin/bash',
        '/usr/sbin/sshd',
        '/etc/apt',
        '/private/var/lib/apt/',
      ];

      for (final path in jailbreakPaths) {
        if (await File(path).exists() || await Directory(path).exists()) {
          return true;
        }
      }

      // Try to write to system directory
      try {
        final file = File('/private/jailbreak_test.txt');
        await file.writeAsString('test');
        await file.delete();
        return true; // If we can write, device is jailbroken
      } catch (e) {
        // Expected on non-jailbroken devices
      }

      return false;
    } catch (e) {
      debugPrint('Error checking iOS jailbreak: $e');
      return false;
    }
  }

  /// Check if Android device is rooted
  static Future<bool> _isAndroidRooted() async {
    try {
      // Check for common root files
      final rootPaths = [
        '/system/app/Superuser.apk',
        '/sbin/su',
        '/system/bin/su',
        '/system/xbin/su',
        '/data/local/xbin/su',
        '/data/local/bin/su',
        '/system/sd/xbin/su',
        '/system/bin/failsafe/su',
        '/data/local/su',
      ];

      for (final path in rootPaths) {
        if (await File(path).exists()) {
          return true;
        }
      }

      // Check for root management apps
      final rootApps = [
        'com.noshufou.android.su',
        'com.noshufou.android.su.elite',
        'eu.chainfire.supersu',
        'com.koushikdutta.superuser',
        'com.thirdparty.superuser',
        'com.yellowes.su',
      ];

      // This would require platform-specific implementation
      // For now, just return false
      return false;
    } catch (e) {
      debugPrint('Error checking Android root: $e');
      return false;
    }
  }

  /// Check if device has screen lock
  static Future<bool> _hasDeviceLock() async {
    try {
      return await _localAuth.canCheckBiometrics ||
          await _localAuth.isDeviceSupported();
    } catch (e) {
      debugPrint('Error checking device lock: $e');
      return false;
    }
  }

  /// Check if debugging is enabled
  static Future<bool> _isDebuggingEnabled() async {
    // This would require platform-specific implementation
    return kDebugMode;
  }

  /// Verify app signature
  static Future<bool> _verifyAppSignature() async {
    // This would require platform-specific implementation
    // For now, just return true in debug mode
    return kDebugMode || true;
  }

  /// Handle security violation
  static Future<void> handleSecurityViolation(
    String violation, {
    String severity = 'medium',
    Map<String, dynamic>? metadata,
  }) async {
    await _auditService?.logAuditEvent(
      eventType: 'security_violation',
      userId: 'system',
      description: violation,
      severity: severity,
      metadata: metadata ?? {},
    );

    if (severity == 'critical') {
      // Consider app termination or emergency lockdown
      debugPrint('CRITICAL SECURITY VIOLATION: $violation');
    }
  }

  /// Check if middleware is initialized
  static bool get isInitialized => _isInitialized;
}

/// Custom security exception
class SecurityException implements Exception {
  final String message;

  const SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}
