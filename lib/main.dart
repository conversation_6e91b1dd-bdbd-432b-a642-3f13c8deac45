import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:firebase_core/firebase_core.dart';

import 'core/config/app_config.dart';
import 'core/di/injection_container.dart';
import 'core/router/app_router.dart';
import 'core/theme/app_theme.dart';
import 'core/services/security_service.dart';
// TODO: Uncomment when getIt is available
// import 'core/services/security_audit_service.dart';
// import 'core/middleware/security_middleware.dart';
// TODO: Create missing BLoC files
// import 'presentation/blocs/auth/auth_bloc.dart';
// import 'presentation/blocs/app/app_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Load environment variables
    await dotenv.load(fileName: ".env");

    // Initialize Firebase
    await Firebase.initializeApp();

    // Initialize Supabase
    await Supabase.initialize(
      url: AppConfig.supabaseUrl,
      anonKey: AppConfig.supabaseAnonKey,
    );

    // Initialize security services
    await SecurityService.initialize();

    // Initialize dependency injection
    await initializeDependencies();

    // TODO: Initialize security middleware when getIt is available
    // final auditService = getIt<SecurityAuditService>();
    // await SecurityMiddleware.initialize(auditService);

    runApp(const PottoApp());
  } catch (e) {
    // Log initialization error
    debugPrint('App initialization failed: $e');

    // Run app with minimal functionality
    runApp(const PottoApp());
  }
}

class PottoApp extends StatelessWidget {
  const PottoApp({super.key});

  @override
  Widget build(BuildContext context) {
    // TODO: Add BLoC providers when BLoC files are created
    return MaterialApp.router(
      title: 'Potto',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode:
          ThemeMode.system, // TODO: Get from state when AppBloc is available
      routerConfig: AppRouter.router,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(
              MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.2),
            ),
          ),
          child: child!,
        );
      },
    );
  }
}
