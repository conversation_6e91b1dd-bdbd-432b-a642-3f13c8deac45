import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../lib/core/services/fraud_prevention_service.dart';
import '../../../lib/core/config/security_config.dart';
import '../../test_utils/test_helpers.dart';
import '../../test_utils/mocks.dart';

void main() {
  group('FraudPreventionService', () {
    late FraudPreventionService fraudService;
    late SharedPreferences mockPrefs;

    setUp(() async {
      await TestHelpers.setupTestEnvironment();
      
      // Setup mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      mockPrefs = await SharedPreferences.getInstance();
      
      fraudService = FraudPreventionService(mockPrefs);
    });

    tearDown(() async {
      await TestHelpers.cleanupTestEnvironment();
    });

    group('Transaction Risk Assessment', () {
      test('should assess low risk for normal transaction', () async {
        final transaction = TransactionData(
          amount: 50.0,
          merchantCategory: 'restaurant',
          location: 'New York, NY',
          timestamp: DateTime.now(),
          userId: 'user-123',
          cardId: 'card-123',
          merchantName: 'Local Restaurant',
        );

        final assessment = await fraudService.assessTransactionRisk(transaction);

        expect(assessment.riskScore, lessThan(SecurityConfig.mediumRiskThreshold));
        expect(assessment.riskLevel, equals(RiskLevel.low));
        expect(assessment.shouldBlock, isFalse);
        expect(assessment.shouldRequireAuth, isFalse);
      });

      test('should assess high risk for large amount transaction', () async {
        final transaction = TransactionData(
          amount: 4500.0, // Close to max limit
          merchantCategory: 'electronics',
          location: 'Unknown',
          timestamp: DateTime.now(),
          userId: 'user-123',
          cardId: 'card-123',
          merchantName: 'Electronics Store',
        );

        final assessment = await fraudService.assessTransactionRisk(transaction);

        expect(assessment.riskScore, greaterThan(SecurityConfig.mediumRiskThreshold));
        expect(assessment.riskLevel, equals(RiskLevel.high));
        expect(assessment.shouldRequireAuth, isTrue);
      });

      test('should block transaction exceeding maximum amount', () async {
        final transaction = TransactionData(
          amount: 6000.0, // Exceeds max limit
          merchantCategory: 'general',
          location: 'Unknown',
          timestamp: DateTime.now(),
          userId: 'user-123',
          cardId: 'card-123',
          merchantName: 'Unknown Merchant',
        );

        final assessment = await fraudService.assessTransactionRisk(transaction);

        expect(assessment.riskScore, greaterThan(SecurityConfig.blockThreshold));
        expect(assessment.riskLevel, equals(RiskLevel.critical));
        expect(assessment.shouldBlock, isTrue);
      });

      test('should assess high risk for high-risk merchant category', () async {
        final transaction = TransactionData(
          amount: 100.0,
          merchantCategory: 'gambling', // High-risk category
          location: 'Las Vegas, NV',
          timestamp: DateTime.now(),
          userId: 'user-123',
          cardId: 'card-123',
          merchantName: 'Casino',
        );

        final assessment = await fraudService.assessTransactionRisk(transaction);

        expect(assessment.riskScore, greaterThan(SecurityConfig.mediumRiskThreshold));
        expect(assessment.riskLevel, equals(RiskLevel.high));
        expect(assessment.shouldRequireAuth, isTrue);
      });

      test('should assess medium risk for medium-risk merchant category', () async {
        final transaction = TransactionData(
          amount: 100.0,
          merchantCategory: 'online_gaming', // Medium-risk category
          location: 'Online',
          timestamp: DateTime.now(),
          userId: 'user-123',
          cardId: 'card-123',
          merchantName: 'Gaming Platform',
        );

        final assessment = await fraudService.assessTransactionRisk(transaction);

        expect(assessment.riskScore, greaterThan(SecurityConfig.lowRiskThreshold));
        expect(assessment.riskScore, lessThan(SecurityConfig.highRiskThreshold));
        expect(assessment.riskLevel, equals(RiskLevel.medium));
      });

      test('should assess high risk for unusual time transaction', () async {
        // Transaction at 3 AM
        final unusualTime = DateTime.now().copyWith(hour: 3, minute: 0);
        
        final transaction = TransactionData(
          amount: 200.0,
          merchantCategory: 'general',
          location: 'Unknown',
          timestamp: unusualTime,
          userId: 'user-123',
          cardId: 'card-123',
          merchantName: 'Late Night Store',
        );

        final assessment = await fraudService.assessTransactionRisk(transaction);

        expect(assessment.riskScore, greaterThan(SecurityConfig.lowRiskThreshold));
        expect(assessment.reasons, contains('Unusual transaction time'));
      });

      test('should assess high risk for foreign location', () async {
        final transaction = TransactionData(
          amount: 100.0,
          merchantCategory: 'general',
          location: 'Moscow, Russia', // Foreign location
          timestamp: DateTime.now(),
          userId: 'user-123',
          cardId: 'card-123',
          merchantName: 'Foreign Merchant',
        );

        final assessment = await fraudService.assessTransactionRisk(transaction);

        expect(assessment.riskScore, greaterThan(SecurityConfig.lowRiskThreshold));
        expect(assessment.reasons, contains('Foreign transaction location'));
      });
    });

    group('Velocity Checking', () {
      test('should detect velocity violation for too many transactions', () async {
        final userId = 'user-123';
        final now = DateTime.now();

        // Simulate multiple transactions in short time
        for (int i = 0; i < SecurityConfig.maxTransactionsPerHour + 1; i++) {
          final transaction = TransactionData(
            amount: 10.0,
            merchantCategory: 'general',
            location: 'Test Location',
            timestamp: now.add(Duration(minutes: i)),
            userId: userId,
            cardId: 'card-123',
            merchantName: 'Test Merchant $i',
          );

          await fraudService.recordTransaction(transaction);
        }

        final hasViolation = await fraudService.checkVelocityViolation(userId);
        expect(hasViolation, isTrue);
      });

      test('should detect velocity violation for excessive daily amount', () async {
        final userId = 'user-123';
        final now = DateTime.now();

        // Simulate transactions exceeding daily limit
        final transaction1 = TransactionData(
          amount: 6000.0,
          merchantCategory: 'general',
          location: 'Test Location',
          timestamp: now,
          userId: userId,
          cardId: 'card-123',
          merchantName: 'Test Merchant 1',
        );

        final transaction2 = TransactionData(
          amount: 5000.0,
          merchantCategory: 'general',
          location: 'Test Location',
          timestamp: now.add(const Duration(hours: 1)),
          userId: userId,
          cardId: 'card-123',
          merchantName: 'Test Merchant 2',
        );

        await fraudService.recordTransaction(transaction1);
        await fraudService.recordTransaction(transaction2);

        final hasViolation = await fraudService.checkVelocityViolation(userId);
        expect(hasViolation, isTrue);
      });

      test('should not detect violation for normal transaction pattern', () async {
        final userId = 'user-123';
        final now = DateTime.now();

        // Simulate normal transactions
        for (int i = 0; i < 3; i++) {
          final transaction = TransactionData(
            amount: 50.0,
            merchantCategory: 'restaurant',
            location: 'Test Location',
            timestamp: now.add(Duration(hours: i * 2)),
            userId: userId,
            cardId: 'card-123',
            merchantName: 'Restaurant $i',
          );

          await fraudService.recordTransaction(transaction);
        }

        final hasViolation = await fraudService.checkVelocityViolation(userId);
        expect(hasViolation, isFalse);
      });
    });

    group('Suspicious Activity Detection', () {
      test('should detect suspicious activity for rapid location changes', () async {
        final userId = 'user-123';
        final now = DateTime.now();

        // Transaction in New York
        final transaction1 = TransactionData(
          amount: 100.0,
          merchantCategory: 'restaurant',
          location: 'New York, NY',
          timestamp: now,
          userId: userId,
          cardId: 'card-123',
          merchantName: 'NYC Restaurant',
        );

        // Transaction in London 30 minutes later (impossible travel)
        final transaction2 = TransactionData(
          amount: 150.0,
          merchantCategory: 'restaurant',
          location: 'London, UK',
          timestamp: now.add(const Duration(minutes: 30)),
          userId: userId,
          cardId: 'card-123',
          merchantName: 'London Restaurant',
        );

        await fraudService.recordTransaction(transaction1);
        await fraudService.recordTransaction(transaction2);

        final isSuspicious = await fraudService.detectSuspiciousActivity(userId);
        expect(isSuspicious, isTrue);
      });

      test('should detect suspicious activity for unusual spending pattern', () async {
        final userId = 'user-123';
        final now = DateTime.now();

        // Record normal spending pattern first
        for (int i = 0; i < 10; i++) {
          final transaction = TransactionData(
            amount: 25.0, // Normal amount
            merchantCategory: 'restaurant',
            location: 'Home City',
            timestamp: now.subtract(Duration(days: i + 1)),
            userId: userId,
            cardId: 'card-123',
            merchantName: 'Local Restaurant',
          );

          await fraudService.recordTransaction(transaction);
        }

        // Sudden large transaction (10x normal)
        final suspiciousTransaction = TransactionData(
          amount: 2500.0,
          merchantCategory: 'electronics',
          location: 'Home City',
          timestamp: now,
          userId: userId,
          cardId: 'card-123',
          merchantName: 'Electronics Store',
        );

        await fraudService.recordTransaction(suspiciousTransaction);

        final isSuspicious = await fraudService.detectSuspiciousActivity(userId);
        expect(isSuspicious, isTrue);
      });

      test('should not detect suspicious activity for normal patterns', () async {
        final userId = 'user-123';
        final now = DateTime.now();

        // Record consistent normal transactions
        for (int i = 0; i < 5; i++) {
          final transaction = TransactionData(
            amount: 50.0 + (i * 10), // Gradually increasing amounts
            merchantCategory: 'restaurant',
            location: 'Home City',
            timestamp: now.subtract(Duration(days: i)),
            userId: userId,
            cardId: 'card-123',
            merchantName: 'Restaurant $i',
          );

          await fraudService.recordTransaction(transaction);
        }

        final isSuspicious = await fraudService.detectSuspiciousActivity(userId);
        expect(isSuspicious, isFalse);
      });
    });

    group('Transaction Recording and Retrieval', () {
      test('should record and retrieve transaction history', () async {
        final userId = 'user-123';
        final transaction = TransactionData(
          amount: 100.0,
          merchantCategory: 'restaurant',
          location: 'Test Location',
          timestamp: DateTime.now(),
          userId: userId,
          cardId: 'card-123',
          merchantName: 'Test Restaurant',
        );

        await fraudService.recordTransaction(transaction);

        final history = await fraudService.getTransactionHistory(userId);
        expect(history, isNotEmpty);
        expect(history.first.amount, equals(100.0));
        expect(history.first.merchantName, equals('Test Restaurant'));
      });

      test('should limit transaction history to specified count', () async {
        final userId = 'user-123';

        // Record multiple transactions
        for (int i = 0; i < 20; i++) {
          final transaction = TransactionData(
            amount: 10.0 * (i + 1),
            merchantCategory: 'general',
            location: 'Test Location',
            timestamp: DateTime.now().subtract(Duration(hours: i)),
            userId: userId,
            cardId: 'card-123',
            merchantName: 'Merchant $i',
          );

          await fraudService.recordTransaction(transaction);
        }

        final history = await fraudService.getTransactionHistory(userId, limit: 10);
        expect(history.length, equals(10));
      });

      test('should return empty history for user with no transactions', () async {
        final userId = 'user-with-no-transactions';
        final history = await fraudService.getTransactionHistory(userId);
        expect(history, isEmpty);
      });
    });

    group('Risk Score Calculation', () {
      test('should calculate risk score based on multiple factors', () async {
        final transaction = TransactionData(
          amount: 1000.0, // High amount
          merchantCategory: 'gambling', // High-risk category
          location: 'Foreign Country', // Foreign location
          timestamp: DateTime.now().copyWith(hour: 2), // Unusual time
          userId: 'user-123',
          cardId: 'card-123',
          merchantName: 'Casino',
        );

        final assessment = await fraudService.assessTransactionRisk(transaction);

        // Should have high risk score due to multiple risk factors
        expect(assessment.riskScore, greaterThan(SecurityConfig.highRiskThreshold));
        expect(assessment.reasons.length, greaterThan(1));
      });

      test('should have lower risk score for trusted patterns', () async {
        final userId = 'user-123';
        final now = DateTime.now();

        // Establish trusted pattern
        for (int i = 0; i < 10; i++) {
          final transaction = TransactionData(
            amount: 50.0,
            merchantCategory: 'restaurant',
            location: 'Home City',
            timestamp: now.subtract(Duration(days: i + 1)),
            userId: userId,
            cardId: 'card-123',
            merchantName: 'Trusted Restaurant',
          );

          await fraudService.recordTransaction(transaction);
        }

        // Similar transaction should have lower risk
        final newTransaction = TransactionData(
          amount: 55.0,
          merchantCategory: 'restaurant',
          location: 'Home City',
          timestamp: now,
          userId: userId,
          cardId: 'card-123',
          merchantName: 'Trusted Restaurant',
        );

        final assessment = await fraudService.assessTransactionRisk(newTransaction);
        expect(assessment.riskScore, lessThan(SecurityConfig.mediumRiskThreshold));
      });
    });

    group('Data Persistence', () {
      test('should persist transaction data across service instances', () async {
        final userId = 'user-123';
        final transaction = TransactionData(
          amount: 100.0,
          merchantCategory: 'restaurant',
          location: 'Test Location',
          timestamp: DateTime.now(),
          userId: userId,
          cardId: 'card-123',
          merchantName: 'Test Restaurant',
        );

        // Record transaction with first service instance
        await fraudService.recordTransaction(transaction);

        // Create new service instance with same SharedPreferences
        final newFraudService = FraudPreventionService(mockPrefs);
        
        // Should be able to retrieve transaction from new instance
        final history = await newFraudService.getTransactionHistory(userId);
        expect(history, isNotEmpty);
        expect(history.first.amount, equals(100.0));
      });
    });
  });
}
