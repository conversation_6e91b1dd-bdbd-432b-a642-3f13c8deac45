import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';

import '../../../lib/core/services/security_service.dart';
import '../../../lib/core/config/security_config.dart';
import '../../test_utils/test_helpers.dart';

// Mock classes for testing
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}
class MockLocalAuthentication extends Mock implements LocalAuthentication {}

void main() {
  group('SecurityService', () {
    late MockFlutterSecureStorage mockSecureStorage;
    late MockLocalAuthentication mockLocalAuth;

    setUp(() async {
      await TestHelpers.setupTestEnvironment();
      mockSecureStorage = MockFlutterSecureStorage();
      mockLocalAuth = MockLocalAuthentication();
    });

    tearDown(() async {
      await TestHelpers.cleanupTestEnvironment();
    });

    group('Encryption', () {
      test('should encrypt and decrypt data correctly', () async {
        const plainText = 'Hello, World!';
        
        // Encrypt the data
        final encryptedData = await SecurityService.encryptData(plainText);
        
        // Verify encryption worked
        expect(encryptedData, isNotEmpty);
        expect(encryptedData, isNot(equals(plainText)));
        
        // Decrypt the data
        final decryptedData = await SecurityService.decryptData(encryptedData);
        
        // Verify decryption worked
        expect(decryptedData, equals(plainText));
      });

      test('should handle empty string encryption', () async {
        const plainText = '';
        
        final encryptedData = await SecurityService.encryptData(plainText);
        final decryptedData = await SecurityService.decryptData(encryptedData);
        
        expect(decryptedData, equals(plainText));
      });

      test('should handle special characters encryption', () async {
        const plainText = '!@#\$%^&*()_+-=[]{}|;:,.<>?';
        
        final encryptedData = await SecurityService.encryptData(plainText);
        final decryptedData = await SecurityService.decryptData(encryptedData);
        
        expect(decryptedData, equals(plainText));
      });

      test('should handle unicode characters encryption', () async {
        const plainText = '🔐🛡️🔒 Security Test 中文 العربية';
        
        final encryptedData = await SecurityService.encryptData(plainText);
        final decryptedData = await SecurityService.decryptData(encryptedData);
        
        expect(decryptedData, equals(plainText));
      });

      test('should produce different encrypted outputs for same input', () async {
        const plainText = 'Same input text';
        
        final encrypted1 = await SecurityService.encryptData(plainText);
        final encrypted2 = await SecurityService.encryptData(plainText);
        
        // Should be different due to random IV
        expect(encrypted1, isNot(equals(encrypted2)));
        
        // But both should decrypt to same plaintext
        final decrypted1 = await SecurityService.decryptData(encrypted1);
        final decrypted2 = await SecurityService.decryptData(encrypted2);
        
        expect(decrypted1, equals(plainText));
        expect(decrypted2, equals(plainText));
      });

      test('should throw exception for invalid encrypted data', () async {
        const invalidEncryptedData = 'invalid-encrypted-data';
        
        expect(
          () => SecurityService.decryptData(invalidEncryptedData),
          throwsException,
        );
      });
    });

    group('Secure Storage', () {
      test('should store and retrieve secure data', () async {
        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});
        when(mockSecureStorage.read(key: anyNamed('key')))
            .thenAnswer((_) async => 'encrypted-value');

        const key = 'test-key';
        const value = 'test-value';

        await SecurityService.storeSecureData(key, value);
        final retrievedValue = await SecurityService.getSecureData(key);

        verify(mockSecureStorage.write(key: key, value: anyNamed('value'))).called(1);
        verify(mockSecureStorage.read(key: key)).called(1);
        expect(retrievedValue, isNotNull);
      });

      test('should delete secure data', () async {
        when(mockSecureStorage.delete(key: anyNamed('key')))
            .thenAnswer((_) async {});

        const key = 'test-key';

        await SecurityService.deleteSecureData(key);

        verify(mockSecureStorage.delete(key: key)).called(1);
      });

      test('should clear all secure data', () async {
        when(mockSecureStorage.deleteAll())
            .thenAnswer((_) async {});

        await SecurityService.clearAllSecureData();

        verify(mockSecureStorage.deleteAll()).called(1);
      });

      test('should return null for non-existent key', () async {
        when(mockSecureStorage.read(key: anyNamed('key')))
            .thenAnswer((_) async => null);

        const key = 'non-existent-key';
        final value = await SecurityService.getSecureData(key);

        expect(value, isNull);
      });
    });

    group('Biometric Authentication', () {
      test('should check if biometric is available', () async {
        when(mockLocalAuth.canCheckBiometrics)
            .thenAnswer((_) async => true);

        final isAvailable = await SecurityService.isBiometricAvailable();

        expect(isAvailable, isTrue);
        verify(mockLocalAuth.canCheckBiometrics).called(1);
      });

      test('should check if biometric is enabled', () async {
        when(mockSecureStorage.read(key: 'biometric_enabled'))
            .thenAnswer((_) async => 'true');

        final isEnabled = await SecurityService.isBiometricEnabled();

        expect(isEnabled, isTrue);
      });

      test('should enable biometric authentication', () async {
        when(mockLocalAuth.canCheckBiometrics)
            .thenAnswer((_) async => true);
        when(mockLocalAuth.getAvailableBiometrics())
            .thenAnswer((_) async => [BiometricType.fingerprint]);
        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});

        final enabled = await SecurityService.enableBiometricAuth();

        expect(enabled, isTrue);
        verify(mockSecureStorage.write(key: 'biometric_enabled', value: 'true')).called(1);
      });

      test('should disable biometric authentication', () async {
        when(mockSecureStorage.write(key: anyNamed('key'), value: anyNamed('value')))
            .thenAnswer((_) async {});

        await SecurityService.disableBiometricAuth();

        verify(mockSecureStorage.write(key: 'biometric_enabled', value: 'false')).called(1);
      });

      test('should authenticate with biometrics successfully', () async {
        when(mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        )).thenAnswer((_) async => true);

        final authenticated = await SecurityService.authenticateWithBiometrics(
          localizedReason: 'Test authentication',
        );

        expect(authenticated, isTrue);
      });

      test('should handle biometric authentication failure', () async {
        when(mockLocalAuth.authenticate(
          localizedReason: anyNamed('localizedReason'),
          options: anyNamed('options'),
        )).thenAnswer((_) async => false);

        final authenticated = await SecurityService.authenticateWithBiometrics(
          localizedReason: 'Test authentication',
        );

        expect(authenticated, isFalse);
      });
    });

    group('Input Validation', () {
      test('should validate safe input', () {
        const safeInput = 'This is a safe input';
        final isSafe = SecurityService.isInputSafe(safeInput);
        expect(isSafe, isTrue);
      });

      test('should detect SQL injection attempts', () {
        const sqlInjection = "'; DROP TABLE users; --";
        final isSafe = SecurityService.isInputSafe(sqlInjection);
        expect(isSafe, isFalse);
      });

      test('should detect XSS attempts', () {
        const xssAttempt = '<script>alert("XSS")</script>';
        final isSafe = SecurityService.isInputSafe(xssAttempt);
        expect(isSafe, isFalse);
      });

      test('should sanitize malicious input', () {
        const maliciousInput = '<script>alert("XSS")</script>Hello World';
        final sanitized = SecurityService.sanitizeInput(maliciousInput);
        expect(sanitized, equals('Hello World'));
        expect(sanitized, isNot(contains('<script>')));
      });
    });

    group('Password Validation', () {
      test('should validate strong password', () {
        const strongPassword = 'StrongP@ssw0rd123';
        final isStrong = SecurityService.isPasswordStrong(strongPassword);
        expect(isStrong, isTrue);
      });

      test('should reject weak password - too short', () {
        const weakPassword = 'Weak1!';
        final isStrong = SecurityService.isPasswordStrong(weakPassword);
        expect(isStrong, isFalse);
      });

      test('should reject weak password - no uppercase', () {
        const weakPassword = 'weakpassword123!';
        final isStrong = SecurityService.isPasswordStrong(weakPassword);
        expect(isStrong, isFalse);
      });

      test('should reject weak password - no lowercase', () {
        const weakPassword = 'WEAKPASSWORD123!';
        final isStrong = SecurityService.isPasswordStrong(weakPassword);
        expect(isStrong, isFalse);
      });

      test('should reject weak password - no numbers', () {
        const weakPassword = 'WeakPassword!';
        final isStrong = SecurityService.isPasswordStrong(weakPassword);
        expect(isStrong, isFalse);
      });

      test('should reject weak password - no special characters', () {
        const weakPassword = 'WeakPassword123';
        final isStrong = SecurityService.isPasswordStrong(weakPassword);
        expect(isStrong, isFalse);
      });

      test('should generate strong password', () {
        final password = SecurityService.generateSecurePassword();
        expect(password.length, greaterThanOrEqualTo(12));
        expect(SecurityService.isPasswordStrong(password), isTrue);
      });

      test('should generate password with custom length', () {
        const customLength = 16;
        final password = SecurityService.generateSecurePassword(length: customLength);
        expect(password.length, equals(customLength));
        expect(SecurityService.isPasswordStrong(password), isTrue);
      });
    });

    group('PIN Validation', () {
      test('should validate strong PIN', () {
        const strongPin = '123789';
        final isStrong = SecurityService.isPinStrong(strongPin);
        expect(isStrong, isTrue);
      });

      test('should reject weak PIN - sequential', () {
        const weakPin = '123456';
        final isStrong = SecurityService.isPinStrong(weakPin);
        expect(isStrong, isFalse);
      });

      test('should reject weak PIN - repeated digits', () {
        const weakPin = '111111';
        final isStrong = SecurityService.isPinStrong(weakPin);
        expect(isStrong, isFalse);
      });

      test('should reject weak PIN - too short', () {
        const weakPin = '123';
        final isStrong = SecurityService.isPinStrong(weakPin);
        expect(isStrong, isFalse);
      });

      test('should reject weak PIN - common patterns', () {
        const commonPins = ['000000', '123456', '654321', '111111'];
        
        for (final pin in commonPins) {
          final isStrong = SecurityService.isPinStrong(pin);
          expect(isStrong, isFalse, reason: 'PIN $pin should be rejected as weak');
        }
      });
    });

    group('Hash Generation', () {
      test('should generate consistent hash for same input', () {
        const input = 'test-input';
        final hash1 = SecurityService.generateHash(input);
        final hash2 = SecurityService.generateHash(input);
        
        expect(hash1, equals(hash2));
        expect(hash1, isNotEmpty);
      });

      test('should generate different hashes for different inputs', () {
        const input1 = 'test-input-1';
        const input2 = 'test-input-2';
        
        final hash1 = SecurityService.generateHash(input1);
        final hash2 = SecurityService.generateHash(input2);
        
        expect(hash1, isNot(equals(hash2)));
      });

      test('should generate hash with salt', () {
        const input = 'test-input';
        const salt = 'test-salt';
        
        final hash1 = SecurityService.generateHash(input);
        final hash2 = SecurityService.generateHash(input, salt: salt);
        
        expect(hash1, isNot(equals(hash2)));
      });
    });

    group('Security Configuration', () {
      test('should have correct encryption settings', () {
        expect(SecurityConfig.encryptionKeyLength, equals(32));
        expect(SecurityConfig.saltLength, equals(16));
        expect(SecurityConfig.ivLength, equals(12));
        expect(SecurityConfig.tagLength, equals(16));
      });

      test('should have correct authentication settings', () {
        expect(SecurityConfig.maxLoginAttempts, equals(5));
        expect(SecurityConfig.lockoutDuration, equals(const Duration(minutes: 15)));
        expect(SecurityConfig.sessionTimeout, equals(const Duration(hours: 24)));
      });

      test('should have correct password requirements', () {
        expect(SecurityConfig.minPasswordLength, equals(8));
        expect(SecurityConfig.requireUppercase, isTrue);
        expect(SecurityConfig.requireLowercase, isTrue);
        expect(SecurityConfig.requireNumbers, isTrue);
        expect(SecurityConfig.requireSpecialChars, isTrue);
      });
    });
  });
}
