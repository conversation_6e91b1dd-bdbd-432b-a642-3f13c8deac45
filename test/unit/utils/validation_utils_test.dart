import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';

import '../../../lib/core/utils/validation_utils.dart';
import '../../test_utils/test_helpers.dart';

void main() {
  group('ValidationUtils', () {
    setUp(() async {
      await TestHelpers.setupTestEnvironment();
    });

    tearDown(() async {
      await TestHelpers.cleanupTestEnvironment();
    });

    group('Email Validation', () {
      test('should validate correct email addresses', () {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in validEmails) {
          expect(
            ValidationUtils.validateEmail(email),
            isNull,
            reason: 'Email $email should be valid',
          );
        }
      });

      test('should reject invalid email addresses', () {
        const invalidEmails = [
          'invalid-email',
          '@example.com',
          'user@',
          'user@.com',
          '<EMAIL>',
          'user@example',
          '',
          'user <EMAIL>',
        ];

        for (final email in invalidEmails) {
          expect(
            ValidationUtils.validateEmail(email),
            isNotNull,
            reason: 'Email $email should be invalid',
          );
        }
      });

      test('should handle null and empty email', () {
        expect(ValidationUtils.validateEmail(null), isNotNull);
        expect(ValidationUtils.validateEmail(''), isNotNull);
        expect(ValidationUtils.validateEmail('   '), isNotNull);
      });
    });

    group('Password Validation', () {
      test('should validate strong passwords', () {
        const strongPasswords = [
          'StrongP@ssw0rd123',
          'MySecure!Pass1',
          'C0mpl3x&P@ssw0rd',
          'Tr0ub4dor&3',
        ];

        for (final password in strongPasswords) {
          expect(
            ValidationUtils.validatePassword(password),
            isNull,
            reason: 'Password $password should be valid',
          );
        }
      });

      test('should reject weak passwords', () {
        const weakPasswords = [
          'weak', // Too short
          'weakpassword', // No uppercase, numbers, special chars
          'WEAKPASSWORD', // No lowercase, numbers, special chars
          'WeakPassword', // No numbers, special chars
          'WeakPassword123', // No special chars
          'WeakPassword!', // No numbers
          '12345678!', // No letters
          '', // Empty
        ];

        for (final password in weakPasswords) {
          expect(
            ValidationUtils.validatePassword(password),
            isNotNull,
            reason: 'Password $password should be invalid',
          );
        }
      });

      test('should handle null password', () {
        expect(ValidationUtils.validatePassword(null), isNotNull);
      });
    });

    group('Phone Number Validation', () {
      test('should validate correct phone numbers', () {
        const validPhones = [
          '+1234567890',
          '+12345678901234',
          '+44123456789',
          '+33123456789',
          '1234567890',
        ];

        for (final phone in validPhones) {
          expect(
            ValidationUtils.validatePhoneNumber(phone),
            isNull,
            reason: 'Phone $phone should be valid',
          );
        }
      });

      test('should reject invalid phone numbers', () {
        const invalidPhones = [
          '+', // Too short
          '123', // Too short
          '+123456789012345678', // Too long
          'abc123456789', // Contains letters
          '+0123456789', // Starts with 0 after country code
          '', // Empty
          '++1234567890', // Double plus
        ];

        for (final phone in invalidPhones) {
          expect(
            ValidationUtils.validatePhoneNumber(phone),
            isNotNull,
            reason: 'Phone $phone should be invalid',
          );
        }
      });

      test('should handle null phone number', () {
        expect(ValidationUtils.validatePhoneNumber(null), isNotNull);
      });
    });

    group('Credit Card Validation', () {
      test('should validate correct credit card numbers', () {
        const validCards = [
          '****************', // Visa test card
          '****************', // Mastercard test card
          '***************', // Amex test card
          '****************', // Discover test card
        ];

        for (final card in validCards) {
          expect(
            ValidationUtils.validateCreditCard(card),
            isNull,
            reason: 'Card $card should be valid',
          );
        }
      });

      test('should reject invalid credit card numbers', () {
        const invalidCards = [
          '1234567890123456', // Invalid Luhn
          '123456789012', // Too short
          '12345678901234567890', // Too long
          'abcd1234567890123', // Contains letters
          '', // Empty
          '0000000000000000', // All zeros
        ];

        for (final card in invalidCards) {
          expect(
            ValidationUtils.validateCreditCard(card),
            isNotNull,
            reason: 'Card $card should be invalid',
          );
        }
      });

      test('should handle null credit card', () {
        expect(ValidationUtils.validateCreditCard(null), isNotNull);
      });
    });

    group('CVV Validation', () {
      test('should validate correct CVV codes', () {
        const validCvvs = ['123', '456', '7890', '000'];

        for (final cvv in validCvvs) {
          expect(
            ValidationUtils.validateCVV(cvv),
            isNull,
            reason: 'CVV $cvv should be valid',
          );
        }
      });

      test('should reject invalid CVV codes', () {
        const invalidCvvs = [
          '12', // Too short
          '12345', // Too long
          'abc', // Contains letters
          '', // Empty
          '12a', // Mixed characters
        ];

        for (final cvv in invalidCvvs) {
          expect(
            ValidationUtils.validateCVV(cvv),
            isNotNull,
            reason: 'CVV $cvv should be invalid',
          );
        }
      });

      test('should handle null CVV', () {
        expect(ValidationUtils.validateCVV(null), isNotNull);
      });
    });

    group('Expiry Date Validation', () {
      test('should validate correct expiry dates', () {
        final futureYear = (DateTime.now().year + 1) % 100;
        final validExpiries = [
          '12/${futureYear.toString().padLeft(2, '0')}',
          '01/${(futureYear + 1).toString().padLeft(2, '0')}',
          '06/${(futureYear + 2).toString().padLeft(2, '0')}',
        ];

        for (final expiry in validExpiries) {
          expect(
            ValidationUtils.validateExpiryDate(expiry),
            isNull,
            reason: 'Expiry $expiry should be valid',
          );
        }
      });

      test('should reject invalid expiry dates', () {
        final pastYear = (DateTime.now().year - 1) % 100;
        final invalidExpiries = [
          '13/25', // Invalid month
          '00/25', // Invalid month
          '12/${pastYear.toString().padLeft(2, '0')}', // Past year
          '12/2025', // Wrong format
          '1/25', // Wrong format
          'ab/25', // Contains letters
          '', // Empty
          '12/', // Incomplete
          '/25', // Incomplete
        ];

        for (final expiry in invalidExpiries) {
          expect(
            ValidationUtils.validateExpiryDate(expiry),
            isNotNull,
            reason: 'Expiry $expiry should be invalid',
          );
        }
      });

      test('should handle null expiry date', () {
        expect(ValidationUtils.validateExpiryDate(null), isNotNull);
      });
    });

    group('Name Validation', () {
      test('should validate correct names', () {
        const validNames = [
          'John Doe',
          'Mary Jane Smith',
          "O'Connor",
          'Jean-Pierre',
          'Dr. Smith',
          'A B',
        ];

        for (final name in validNames) {
          expect(
            ValidationUtils.validateName(name),
            isNull,
            reason: 'Name $name should be valid',
          );
        }
      });

      test('should reject invalid names', () {
        const invalidNames = [
          'A', // Too short
          'A' * 51, // Too long
          'John123', // Contains numbers
          'John@Doe', // Contains special chars
          '', // Empty
          '   ', // Only spaces
        ];

        for (final name in invalidNames) {
          expect(
            ValidationUtils.validateName(name),
            isNotNull,
            reason: 'Name $name should be invalid',
          );
        }
      });

      test('should handle null name', () {
        expect(ValidationUtils.validateName(null), isNotNull);
      });
    });

    group('Amount Validation', () {
      test('should validate correct amounts', () {
        const validAmounts = [
          '0',
          '0.00',
          '10',
          '10.50',
          '1000.99',
          '0.01',
        ];

        for (final amount in validAmounts) {
          expect(
            ValidationUtils.validateAmount(amount),
            isNull,
            reason: 'Amount $amount should be valid',
          );
        }
      });

      test('should reject invalid amounts', () {
        const invalidAmounts = [
          '-10', // Negative
          '10.123', // Too many decimal places
          'abc', // Not a number
          '', // Empty
          '10.', // Incomplete decimal
          '.50', // Missing leading zero
          '10,50', // Wrong decimal separator
        ];

        for (final amount in invalidAmounts) {
          expect(
            ValidationUtils.validateAmount(amount),
            isNotNull,
            reason: 'Amount $amount should be invalid',
          );
        }
      });

      test('should handle null amount', () {
        expect(ValidationUtils.validateAmount(null), isNotNull);
      });
    });

    group('Security Validation', () {
      test('should detect SQL injection attempts', () {
        const sqlInjections = [
          "'; DROP TABLE users; --",
          "' OR '1'='1",
          "admin'--",
          "' UNION SELECT * FROM users --",
          "1; DELETE FROM users",
        ];

        for (final injection in sqlInjections) {
          expect(
            ValidationUtils.containsSQLInjection(injection),
            isTrue,
            reason: 'Should detect SQL injection in: $injection',
          );
        }
      });

      test('should not flag safe input as SQL injection', () {
        const safeInputs = [
          "John's Restaurant",
          "It's a beautiful day",
          "Price: \$10.99",
          "Email: <EMAIL>",
          "Normal text input",
        ];

        for (final input in safeInputs) {
          expect(
            ValidationUtils.containsSQLInjection(input),
            isFalse,
            reason: 'Should not flag safe input as SQL injection: $input',
          );
        }
      });

      test('should detect XSS attempts', () {
        const xssAttempts = [
          '<script>alert("XSS")</script>',
          '<img src="x" onerror="alert(1)">',
          'javascript:alert("XSS")',
          '<iframe src="javascript:alert(1)"></iframe>',
          '<svg onload="alert(1)">',
        ];

        for (final xss in xssAttempts) {
          expect(
            ValidationUtils.containsXSS(xss),
            isTrue,
            reason: 'Should detect XSS in: $xss',
          );
        }
      });

      test('should not flag safe HTML as XSS', () {
        const safeHtml = [
          'Hello <world>',
          'Price < 10',
          'A > B',
          'Email: <EMAIL>',
          'Normal text',
        ];

        for (final html in safeHtml) {
          expect(
            ValidationUtils.containsXSS(html),
            isFalse,
            reason: 'Should not flag safe HTML as XSS: $html',
          );
        }
      });
    });

    group('Input Formatters', () {
      test('should provide correct phone formatters', () {
        final formatters = ValidationUtils.getPhoneFormatters();
        expect(formatters, isNotEmpty);
        expect(formatters.any((f) => f is FilteringTextInputFormatter), isTrue);
        expect(formatters.any((f) => f is LengthLimitingTextInputFormatter), isTrue);
      });

      test('should provide correct amount formatters', () {
        final formatters = ValidationUtils.getAmountFormatters();
        expect(formatters, isNotEmpty);
        expect(formatters.any((f) => f is FilteringTextInputFormatter), isTrue);
        expect(formatters.any((f) => f is LengthLimitingTextInputFormatter), isTrue);
      });

      test('should provide correct credit card formatters', () {
        final formatters = ValidationUtils.getCreditCardFormatters();
        expect(formatters, isNotEmpty);
        expect(formatters.any((f) => f is FilteringTextInputFormatter), isTrue);
        expect(formatters.any((f) => f is LengthLimitingTextInputFormatter), isTrue);
      });

      test('should allow custom decimal places for amount formatters', () {
        final formatters = ValidationUtils.getAmountFormatters(decimalPlaces: 3);
        expect(formatters, isNotEmpty);
        
        // Test that it allows 3 decimal places
        final filterFormatter = formatters.firstWhere(
          (f) => f is FilteringTextInputFormatter,
        ) as FilteringTextInputFormatter;
        
        // This would need more complex testing to verify the regex pattern
        expect(filterFormatter, isNotNull);
      });
    });

    group('Luhn Algorithm', () {
      test('should validate correct credit card numbers using Luhn', () {
        const validCards = [
          '****************', // Visa
          '****************', // Mastercard
          '***************', // Amex
          '****************', // Discover
        ];

        for (final card in validCards) {
          expect(
            ValidationUtils.isValidLuhn(card),
            isTrue,
            reason: 'Card $card should pass Luhn validation',
          );
        }
      });

      test('should reject invalid credit card numbers using Luhn', () {
        const invalidCards = [
          '****************', // Invalid Luhn
          '****************', // Invalid Luhn
          '1234567890123456', // Invalid Luhn
        ];

        for (final card in invalidCards) {
          expect(
            ValidationUtils.isValidLuhn(card),
            isFalse,
            reason: 'Card $card should fail Luhn validation',
          );
        }
      });
    });
  });
}
