import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';

import '../../lib/core/services/storage_service.dart';
import '../../lib/core/services/stripe_service.dart';
import '../../lib/core/services/stripe_issuing_service.dart';
import '../../lib/core/services/notification_service.dart';
import '../../lib/core/services/realtime_service.dart';
import '../../lib/core/services/chat_service.dart';
import '../../lib/core/services/webhook_service.dart';
import '../../lib/core/services/security_service.dart';
import '../../lib/core/services/fraud_prevention_service.dart';
import '../../lib/core/services/security_audit_service.dart';
import '../../lib/core/network/api_client.dart';

// Generate mocks for all the services and external dependencies
@GenerateMocks([
  // External dependencies
  SupabaseClient,
  GoTrueClient,
  PostgrestClient,
  PostgrestQueryBuilder,
  PostgrestFilterBuilder,
  RealtimeClient,
  RealtimeChannel,
  SharedPreferences,
  Dio,
  
  // Core services
  StorageService,
  StripeService,
  StripeIssuingService,
  NotificationService,
  RealtimeService,
  ChatService,
  WebhookService,
  FraudPreventionService,
  SecurityAuditService,
  
  // Network
  ApiClient,
])
void main() {}

// Manual mocks for complex classes that need custom behavior
class MockSupabaseClient extends Mock implements SupabaseClient {
  final MockGoTrueClient _auth = MockGoTrueClient();
  final MockPostgrestClient _postgrest = MockPostgrestClient();
  final MockRealtimeClient _realtime = MockRealtimeClient();

  @override
  GoTrueClient get auth => _auth;

  @override
  PostgrestClient get postgrest => _postgrest;

  @override
  RealtimeClient get realtime => _realtime;

  @override
  PostgrestQueryBuilder from(String table) => MockPostgrestQueryBuilder();

  @override
  PostgrestQueryBuilder rpc(String fn, {Map<String, dynamic>? params}) => MockPostgrestQueryBuilder();
}

class MockGoTrueClient extends Mock implements GoTrueClient {
  User? _currentUser;
  Session? _currentSession;

  @override
  User? get currentUser => _currentUser;

  @override
  Session? get currentSession => _currentSession;

  void setCurrentUser(User? user) {
    _currentUser = user;
  }

  void setCurrentSession(Session? session) {
    _currentSession = session;
  }

  @override
  Future<AuthResponse> signUp({
    String? email,
    String? password,
    String? phone,
    Map<String, dynamic>? data,
    String? redirectTo,
    String? captchaToken,
  }) async {
    // Mock successful signup
    final user = User(
      id: 'mock-user-id',
      appMetadata: {},
      userMetadata: data ?? {},
      aud: 'authenticated',
      createdAt: DateTime.now().toIso8601String(),
      email: email,
    );
    
    final session = Session(
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      expiresIn: 3600,
      tokenType: 'bearer',
      user: user,
    );

    _currentUser = user;
    _currentSession = session;

    return AuthResponse(user: user, session: session);
  }

  @override
  Future<AuthResponse> signInWithPassword({
    String? email,
    String? phone,
    required String password,
    String? captchaToken,
  }) async {
    // Mock successful login
    final user = User(
      id: 'mock-user-id',
      appMetadata: {},
      userMetadata: {'email': email},
      aud: 'authenticated',
      createdAt: DateTime.now().toIso8601String(),
      email: email,
    );
    
    final session = Session(
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      expiresIn: 3600,
      tokenType: 'bearer',
      user: user,
    );

    _currentUser = user;
    _currentSession = session;

    return AuthResponse(user: user, session: session);
  }

  @override
  Future<void> signOut({SignOutScope scope = SignOutScope.global}) async {
    _currentUser = null;
    _currentSession = null;
  }
}

class MockPostgrestClient extends Mock implements PostgrestClient {
  @override
  PostgrestQueryBuilder from(String table) => MockPostgrestQueryBuilder();

  @override
  PostgrestQueryBuilder rpc(String fn, {Map<String, dynamic>? params}) => MockPostgrestQueryBuilder();
}

class MockPostgrestQueryBuilder extends Mock implements PostgrestQueryBuilder {
  Map<String, dynamic>? _mockData;
  List<Map<String, dynamic>>? _mockList;
  Exception? _mockError;

  void setMockData(Map<String, dynamic> data) {
    _mockData = data;
    _mockList = null;
    _mockError = null;
  }

  void setMockList(List<Map<String, dynamic>> list) {
    _mockList = list;
    _mockData = null;
    _mockError = null;
  }

  void setMockError(Exception error) {
    _mockError = error;
    _mockData = null;
    _mockList = null;
  }

  @override
  PostgrestQueryBuilder select([String columns = '*']) => this;

  @override
  PostgrestQueryBuilder insert(Object values) => this;

  @override
  PostgrestQueryBuilder update(Map<String, dynamic> values) => this;

  @override
  PostgrestQueryBuilder delete() => this;

  @override
  PostgrestQueryBuilder eq(String column, Object value) => this;

  @override
  PostgrestQueryBuilder neq(String column, Object value) => this;

  @override
  PostgrestQueryBuilder gt(String column, Object value) => this;

  @override
  PostgrestQueryBuilder gte(String column, Object value) => this;

  @override
  PostgrestQueryBuilder lt(String column, Object value) => this;

  @override
  PostgrestQueryBuilder lte(String column, Object value) => this;

  @override
  PostgrestQueryBuilder like(String column, String pattern) => this;

  @override
  PostgrestQueryBuilder ilike(String column, String pattern) => this;

  @override
  PostgrestQueryBuilder is_(String column, Object value) => this;

  @override
  PostgrestQueryBuilder in_(String column, List values) => this;

  @override
  PostgrestQueryBuilder contains(String column, Object value) => this;

  @override
  PostgrestQueryBuilder containedBy(String column, Object value) => this;

  @override
  PostgrestQueryBuilder rangeLt(String column, String range) => this;

  @override
  PostgrestQueryBuilder rangeGt(String column, String range) => this;

  @override
  PostgrestQueryBuilder rangeGte(String column, String range) => this;

  @override
  PostgrestQueryBuilder rangeLte(String column, String range) => this;

  @override
  PostgrestQueryBuilder rangeAdjacent(String column, String range) => this;

  @override
  PostgrestQueryBuilder overlaps(String column, Object value) => this;

  @override
  PostgrestQueryBuilder textSearch(String column, String query, {PostgrestTextSearchType? type, String? config}) => this;

  @override
  PostgrestQueryBuilder match(Map<String, dynamic> query) => this;

  @override
  PostgrestQueryBuilder not(String column, String operator, Object value) => this;

  @override
  PostgrestQueryBuilder or(String filters) => this;

  @override
  PostgrestQueryBuilder filter(String column, String operator, Object value) => this;

  @override
  PostgrestQueryBuilder order(String column, {bool ascending = true, bool nullsFirst = false}) => this;

  @override
  PostgrestQueryBuilder limit(int count, {int? foreignTable}) => this;

  @override
  PostgrestQueryBuilder range(int from, int to, {int? foreignTable}) => this;

  @override
  PostgrestQueryBuilder single() => this;

  @override
  PostgrestQueryBuilder maybeSingle() => this;

  @override
  Future<PostgrestResponse<T>> then<T>(FutureOr<PostgrestResponse<T>> Function(PostgrestResponse) onValue, {Function? onError}) async {
    if (_mockError != null) {
      throw _mockError!;
    }

    if (_mockData != null) {
      return PostgrestResponse<T>(
        data: _mockData as T,
        status: 200,
        count: 1,
      );
    }

    if (_mockList != null) {
      return PostgrestResponse<T>(
        data: _mockList as T,
        status: 200,
        count: _mockList!.length,
      );
    }

    return PostgrestResponse<T>(
      data: [] as T,
      status: 200,
      count: 0,
    );
  }
}

class MockRealtimeClient extends Mock implements RealtimeClient {
  @override
  RealtimeChannel channel(String topic, {RealtimeChannelConfig? opts}) => MockRealtimeChannel();

  @override
  Future<void> connect() async {}

  @override
  Future<void> disconnect() async {}
}

class MockRealtimeChannel extends Mock implements RealtimeChannel {
  @override
  RealtimeChannel on(String event, void Function(dynamic payload) callback) => this;

  @override
  Future<String> subscribe() async => 'ok';

  @override
  Future<String> unsubscribe() async => 'ok';
}

// Mock data builders
class MockDataBuilder {
  static User createMockUser({
    String id = 'mock-user-id',
    String email = '<EMAIL>',
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id,
      appMetadata: {},
      userMetadata: metadata ?? {'email': email},
      aud: 'authenticated',
      createdAt: DateTime.now().toIso8601String(),
      email: email,
    );
  }

  static Session createMockSession({
    String accessToken = 'mock-access-token',
    String refreshToken = 'mock-refresh-token',
    User? user,
  }) {
    return Session(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresIn: 3600,
      tokenType: 'bearer',
      user: user ?? createMockUser(),
    );
  }

  static Map<String, dynamic> createMockWallet({
    String id = 'mock-wallet-id',
    String name = 'Test Wallet',
    String description = 'Test wallet description',
    double balance = 100.0,
    String ownerId = 'mock-user-id',
  }) {
    return {
      'id': id,
      'name': name,
      'description': description,
      'balance': balance,
      'owner_id': ownerId,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  static Map<String, dynamic> createMockTransaction({
    String id = 'mock-transaction-id',
    String walletId = 'mock-wallet-id',
    String userId = 'mock-user-id',
    double amount = 50.0,
    String description = 'Test transaction',
    String type = 'expense',
  }) {
    return {
      'id': id,
      'wallet_id': walletId,
      'user_id': userId,
      'amount': amount,
      'description': description,
      'type': type,
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  static Map<String, dynamic> createMockInvitation({
    String id = 'mock-invitation-id',
    String walletId = 'mock-wallet-id',
    String inviterId = 'mock-user-id',
    String email = '<EMAIL>',
    String status = 'pending',
  }) {
    return {
      'id': id,
      'wallet_id': walletId,
      'inviter_id': inviterId,
      'email': email,
      'status': status,
      'created_at': DateTime.now().toIso8601String(),
    };
  }
}
