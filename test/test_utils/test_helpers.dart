import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../lib/core/di/injection_container.dart';
import '../../lib/core/theme/app_theme.dart';
import 'mocks.dart';

/// Test utilities and helpers for consistent testing setup
class TestHelpers {
  /// Setup test environment with mocked dependencies
  static Future<void> setupTestEnvironment() async {
    // Reset GetIt
    if (getIt.isRegistered<MockSupabaseClient>()) {
      await getIt.reset();
    }

    // Setup SharedPreferences for testing
    SharedPreferences.setMockInitialValues({});
    final sharedPreferences = await SharedPreferences.getInstance();

    // Register mock dependencies
    getIt.registerSingleton<SupabaseClient>(MockSupabaseClient());
    getIt.registerSingleton<SharedPreferences>(sharedPreferences);
  }

  /// Create a test widget with necessary providers
  static Widget createTestWidget({
    required Widget child,
    List<BlocProvider>? providers,
    ThemeData? theme,
  }) {
    return MaterialApp(
      theme: theme ?? AppTheme.lightTheme,
      home: providers != null
          ? MultiBlocProvider(
              providers: providers,
              child: child,
            )
          : child,
    );
  }

  /// Create a test widget with navigation
  static Widget createTestWidgetWithNavigation({
    required Widget child,
    List<BlocProvider>? providers,
    ThemeData? theme,
    String initialRoute = '/',
    Map<String, WidgetBuilder>? routes,
  }) {
    return MaterialApp(
      theme: theme ?? AppTheme.lightTheme,
      initialRoute: initialRoute,
      routes: routes ?? {'/': (context) => child},
      home: providers != null
          ? MultiBlocProvider(
              providers: providers,
              child: child,
            )
          : child,
    );
  }

  /// Pump and settle with custom duration
  static Future<void> pumpAndSettleWithDelay(
    WidgetTester tester, {
    Duration delay = const Duration(milliseconds: 100),
  }) async {
    await tester.pump(delay);
    await tester.pumpAndSettle();
  }

  /// Find widget by key with type safety
  static T findWidgetByKey<T extends Widget>(
    WidgetTester tester,
    Key key,
  ) {
    final finder = find.byKey(key);
    expect(finder, findsOneWidget);
    return tester.widget<T>(finder);
  }

  /// Find widget by type with optional index
  static T findWidgetByType<T extends Widget>(
    WidgetTester tester, {
    int index = 0,
  }) {
    final finder = find.byType(T);
    expect(finder, findsWidgets);
    return tester.widget<T>(finder.at(index));
  }

  /// Tap widget by key
  static Future<void> tapByKey(
    WidgetTester tester,
    Key key,
  ) async {
    final finder = find.byKey(key);
    expect(finder, findsOneWidget);
    await tester.tap(finder);
    await tester.pumpAndSettle();
  }

  /// Tap widget by type
  static Future<void> tapByType<T extends Widget>(
    WidgetTester tester, {
    int index = 0,
  }) async {
    final finder = find.byType(T).at(index);
    expect(finder, findsOneWidget);
    await tester.tap(finder);
    await tester.pumpAndSettle();
  }

  /// Enter text in text field by key
  static Future<void> enterTextByKey(
    WidgetTester tester,
    Key key,
    String text,
  ) async {
    final finder = find.byKey(key);
    expect(finder, findsOneWidget);
    await tester.enterText(finder, text);
    await tester.pumpAndSettle();
  }

  /// Scroll until widget is visible
  static Future<void> scrollUntilVisible(
    WidgetTester tester,
    Finder finder,
    Finder scrollable, {
    double delta = 100.0,
    AxisDirection scrollDirection = AxisDirection.down,
  }) async {
    while (finder.evaluate().isEmpty) {
      await tester.drag(scrollable, Offset(0, -delta));
      await tester.pumpAndSettle();
    }
  }

  /// Verify widget exists
  static void verifyWidgetExists<T extends Widget>({
    Key? key,
    String? text,
    int expectedCount = 1,
  }) {
    Finder finder;
    
    if (key != null) {
      finder = find.byKey(key);
    } else if (text != null) {
      finder = find.text(text);
    } else {
      finder = find.byType(T);
    }

    if (expectedCount == 1) {
      expect(finder, findsOneWidget);
    } else if (expectedCount == 0) {
      expect(finder, findsNothing);
    } else {
      expect(finder, findsNWidgets(expectedCount));
    }
  }

  /// Verify widget does not exist
  static void verifyWidgetNotExists<T extends Widget>({
    Key? key,
    String? text,
  }) {
    verifyWidgetExists<T>(
      key: key,
      text: text,
      expectedCount: 0,
    );
  }

  /// Wait for widget to appear
  static Future<void> waitForWidget(
    WidgetTester tester,
    Finder finder, {
    Duration timeout = const Duration(seconds: 5),
  }) async {
    final endTime = DateTime.now().add(timeout);
    
    while (DateTime.now().isBefore(endTime)) {
      await tester.pump(const Duration(milliseconds: 100));
      
      if (finder.evaluate().isNotEmpty) {
        return;
      }
    }
    
    throw TimeoutException(
      'Widget not found within timeout',
      timeout,
    );
  }

  /// Simulate network delay
  static Future<void> simulateNetworkDelay({
    Duration delay = const Duration(milliseconds: 500),
  }) async {
    await Future.delayed(delay);
  }

  /// Create mock user data
  static Map<String, dynamic> createMockUser({
    String id = 'test-user-id',
    String email = '<EMAIL>',
    String name = 'Test User',
  }) {
    return {
      'id': id,
      'email': email,
      'user_metadata': {
        'name': name,
      },
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  /// Create mock wallet data
  static Map<String, dynamic> createMockWallet({
    String id = 'test-wallet-id',
    String name = 'Test Wallet',
    String description = 'Test wallet description',
    double balance = 100.0,
    String ownerId = 'test-user-id',
  }) {
    return {
      'id': id,
      'name': name,
      'description': description,
      'balance': balance,
      'owner_id': ownerId,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  /// Create mock transaction data
  static Map<String, dynamic> createMockTransaction({
    String id = 'test-transaction-id',
    String walletId = 'test-wallet-id',
    String userId = 'test-user-id',
    double amount = 50.0,
    String description = 'Test transaction',
    String type = 'expense',
  }) {
    return {
      'id': id,
      'wallet_id': walletId,
      'user_id': userId,
      'amount': amount,
      'description': description,
      'type': type,
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  /// Setup mock responses for common API calls
  static void setupMockResponses(MockSupabaseClient mockClient) {
    // Mock auth responses
    when(mockClient.auth.currentUser).thenReturn(null);
    when(mockClient.auth.currentSession).thenReturn(null);

    // Mock database responses
    final mockQueryBuilder = MockPostgrestQueryBuilder();
    when(mockClient.from(any)).thenReturn(mockQueryBuilder);
    
    // Setup default empty responses
    when(mockQueryBuilder.select(any)).thenReturn(mockQueryBuilder);
    when(mockQueryBuilder.insert(any)).thenReturn(mockQueryBuilder);
    when(mockQueryBuilder.update(any)).thenReturn(mockQueryBuilder);
    when(mockQueryBuilder.delete()).thenReturn(mockQueryBuilder);
    when(mockQueryBuilder.eq(any, any)).thenReturn(mockQueryBuilder);
    when(mockQueryBuilder.order(any)).thenReturn(mockQueryBuilder);
    when(mockQueryBuilder.limit(any)).thenReturn(mockQueryBuilder);
  }

  /// Cleanup test environment
  static Future<void> cleanupTestEnvironment() async {
    await getIt.reset();
  }
}

/// Custom timeout exception for testing
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  const TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message (timeout: $timeout)';
}

/// Test data generators
class TestDataGenerator {
  static const List<String> _firstNames = [
    'John', 'Jane', 'Mike', 'Sarah', 'David', 'Emma', 'Chris', 'Lisa'
  ];
  
  static const List<String> _lastNames = [
    'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'
  ];

  static const List<String> _walletNames = [
    'Vacation Fund', 'Dinner Group', 'Office Lunch', 'Weekend Trip', 
    'Birthday Party', 'Movie Night', 'Concert Tickets', 'Gift Fund'
  ];

  /// Generate random user data
  static Map<String, dynamic> generateUser() {
    final firstName = _firstNames[DateTime.now().millisecond % _firstNames.length];
    final lastName = _lastNames[DateTime.now().microsecond % _lastNames.length];
    final email = '${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com';
    
    return TestHelpers.createMockUser(
      id: 'user-${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      name: '$firstName $lastName',
    );
  }

  /// Generate random wallet data
  static Map<String, dynamic> generateWallet() {
    final name = _walletNames[DateTime.now().millisecond % _walletNames.length];
    
    return TestHelpers.createMockWallet(
      id: 'wallet-${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: 'Generated test wallet for $name',
      balance: (DateTime.now().millisecond % 1000).toDouble(),
    );
  }

  /// Generate multiple test items
  static List<T> generateList<T>(T Function() generator, int count) {
    return List.generate(count, (index) => generator());
  }
}
