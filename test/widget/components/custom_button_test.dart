import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../lib/presentation/widgets/common/custom_button.dart';
import '../../test_utils/test_helpers.dart';

void main() {
  group('CustomButton Widget Tests', () {
    setUp(() async {
      await TestHelpers.setupTestEnvironment();
    });

    tearDown(() async {
      await TestHelpers.cleanupTestEnvironment();
    });

    testWidgets('should display button with text', (WidgetTester tester) async {
      const buttonText = 'Test Button';
      bool wasPressed = false;

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: buttonText,
            onPressed: () => wasPressed = true,
          ),
        ),
      );

      // Verify button is displayed with correct text
      expect(find.text(buttonText), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);

      // Test button press
      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();

      expect(wasPressed, isTrue);
    });

    testWidgets('should show loading indicator when loading', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Loading Button',
            onPressed: () {},
            isLoading: true,
          ),
        ),
      );

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      
      // Button should be disabled when loading
      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNull);
    });

    testWidgets('should be disabled when onPressed is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: const CustomButton(
            text: 'Disabled Button',
            onPressed: null,
          ),
        ),
      );

      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.onPressed, isNull);

      // Text should still be visible
      expect(find.text('Disabled Button'), findsOneWidget);
    });

    testWidgets('should apply full width when specified', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: const CustomButton(
            text: 'Full Width Button',
            onPressed: null,
            isFullWidth: true,
          ),
        ),
      );

      final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
      expect(sizedBox.width, equals(double.infinity));
    });

    testWidgets('should apply primary button variant by default', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Primary Button',
            onPressed: () {},
          ),
        ),
      );

      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.style, isNotNull);
    });

    testWidgets('should apply secondary button variant', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Secondary Button',
            onPressed: () {},
            variant: ButtonVariant.secondary,
          ),
        ),
      );

      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.style, isNotNull);
    });

    testWidgets('should apply outline button variant', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Outline Button',
            onPressed: () {},
            variant: ButtonVariant.outline,
          ),
        ),
      );

      // Should still use ElevatedButton but with different styling
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should display icon when provided', (WidgetTester tester) async {
      const icon = Icons.add;

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Button with Icon',
            onPressed: () {},
            icon: Icon(icon),
          ),
        ),
      );

      expect(find.byIcon(icon), findsOneWidget);
      expect(find.text('Button with Icon'), findsOneWidget);
    });

    testWidgets('should apply different button sizes', (WidgetTester tester) async {
      // Test small size
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Small Button',
            onPressed: () {},
            size: ButtonSize.small,
          ),
        ),
      );

      final smallButton = tester.widget<SizedBox>(find.byType(SizedBox));
      expect(smallButton.height, equals(36));

      // Test large size
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Large Button',
            onPressed: () {},
            size: ButtonSize.large,
          ),
        ),
      );

      final largeButton = tester.widget<SizedBox>(find.byType(SizedBox));
      expect(largeButton.height, equals(52));
    });

    testWidgets('should handle theme changes correctly', (WidgetTester tester) async {
      // Test with light theme
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          theme: ThemeData.light(),
          child: CustomButton(
            text: 'Theme Test',
            onPressed: () {},
          ),
        ),
      );

      expect(find.byType(ElevatedButton), findsOneWidget);

      // Test with dark theme
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          theme: ThemeData.dark(),
          child: CustomButton(
            text: 'Theme Test',
            onPressed: () {},
          ),
        ),
      );

      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should apply danger variant correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Danger Button',
            onPressed: () {},
            variant: ButtonVariant.danger,
          ),
        ),
      );

      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.style, isNotNull);
    });

    testWidgets('should apply ghost variant correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Ghost Button',
            onPressed: () {},
            variant: ButtonVariant.ghost,
          ),
        ),
      );

      final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(button.style, isNotNull);
    });

    testWidgets('should maintain consistent size when loading', (WidgetTester tester) async {
      // First measure normal button
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Normal Button',
            onPressed: () {},
          ),
        ),
      );

      final normalSize = tester.getSize(find.byType(SizedBox));

      // Then measure loading button
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Loading Button',
            onPressed: () {},
            isLoading: true,
          ),
        ),
      );

      final loadingSize = tester.getSize(find.byType(SizedBox));

      // Sizes should be the same
      expect(loadingSize.width, equals(normalSize.width));
      expect(loadingSize.height, equals(normalSize.height));
    });

    testWidgets('should handle accessibility correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomButton(
            text: 'Accessible Button',
            onPressed: () {},
          ),
        ),
      );

      // Button should be accessible
      expect(find.byType(ElevatedButton), findsOneWidget);
      
      // Should have semantic label
      expect(find.text('Accessible Button'), findsOneWidget);
    });
  });
}
