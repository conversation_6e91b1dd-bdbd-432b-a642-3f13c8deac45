import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../lib/presentation/widgets/common/custom_text_field.dart';
import '../../test_utils/test_helpers.dart';

void main() {
  group('CustomTextField Widget Tests', () {
    setUp(() async {
      await TestHelpers.setupTestEnvironment();
    });

    tearDown(() async {
      await TestHelpers.cleanupTestEnvironment();
    });

    testWidgets('should display text field with label',
        (WidgetTester tester) async {
      const labelText = 'Test Label';
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: labelText,
            controller: controller,
          ),
        ),
      );

      expect(find.text(labelText), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should display hint text', (WidgetTester tester) async {
      const hintText = 'Enter your text here';
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            hint: hintText,
            controller: controller,
          ),
        ),
      );

      expect(find.text(hintText), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should handle text input correctly',
        (WidgetTester tester) async {
      const inputText = 'Hello, World!';
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Input Field',
            controller: controller,
          ),
        ),
      );

      // Enter text
      await tester.enterText(find.byType(TextFormField), inputText);
      await tester.pumpAndSettle();

      expect(controller.text, equals(inputText));
      expect(find.text(inputText), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should handle onChanged callback',
        (WidgetTester tester) async {
      String? changedValue;
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Callback Field',
            controller: controller,
            onChanged: (value) => changedValue = value,
          ),
        ),
      );

      const testText = 'Test callback';
      await tester.enterText(find.byType(TextFormField), testText);
      await tester.pumpAndSettle();

      expect(changedValue, equals(testText));

      controller.dispose();
    });

    testWidgets('should display error text when provided',
        (WidgetTester tester) async {
      const errorText = 'This field is required';
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Error Field',
            controller: controller,
            errorText: errorText,
          ),
        ),
      );

      expect(find.text(errorText), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should display helper text when provided',
        (WidgetTester tester) async {
      const helperText = 'This is helper text';
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Helper Field',
            controller: controller,
            helperText: helperText,
          ),
        ),
      );

      expect(find.text(helperText), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should handle password visibility toggle',
        (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Password',
            controller: controller,
            obscureText: true,
          ),
        ),
      );

      // Enter password text
      await tester.enterText(find.byType(TextFormField), 'password123');
      await tester.pumpAndSettle();

      // Text should be obscured
      expect(find.text('password123'), findsNothing);

      controller.dispose();
    });

    testWidgets('should display prefix icon when provided',
        (WidgetTester tester) async {
      const prefixIcon = Icons.email;
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Email',
            controller: controller,
            prefixIcon: Icon(prefixIcon),
          ),
        ),
      );

      expect(find.byIcon(prefixIcon), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should display suffix icon when provided',
        (WidgetTester tester) async {
      const suffixIcon = Icons.search;
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Search',
            controller: controller,
            suffixIcon: Icon(suffixIcon),
          ),
        ),
      );

      expect(find.byIcon(suffixIcon), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should handle different keyboard types',
        (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Number Field',
            controller: controller,
            keyboardType: TextInputType.number,
          ),
        ),
      );

      // Just verify the widget is created correctly
      expect(find.byType(TextFormField), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should handle max lines correctly',
        (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Multi-line Field',
            controller: controller,
            maxLines: 3,
          ),
        ),
      );

      // Just verify the widget is created correctly
      expect(find.byType(TextFormField), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should handle max length correctly',
        (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Limited Field',
            controller: controller,
            maxLength: 10,
          ),
        ),
      );

      // Just verify the widget is created correctly
      expect(find.byType(TextFormField), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should be disabled when enabled is false',
        (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Disabled Field',
            controller: controller,
            enabled: false,
          ),
        ),
      );

      final textField =
          tester.widget<TextFormField>(find.byType(TextFormField));
      expect(textField.enabled, isFalse);

      controller.dispose();
    });

    testWidgets('should be read-only when readOnly is true',
        (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Read-only Field',
            controller: controller,
            readOnly: true,
          ),
        ),
      );

      // Just verify the widget is created correctly
      expect(find.byType(TextFormField), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should handle onTap callback', (WidgetTester tester) async {
      bool wasTapped = false;
      final controller = TextEditingController();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Tappable Field',
            controller: controller,
            onTap: () => wasTapped = true,
          ),
        ),
      );

      await tester.tap(find.byType(TextFormField));
      await tester.pumpAndSettle();

      expect(wasTapped, isTrue);

      controller.dispose();
    });

    testWidgets('should apply different variants correctly',
        (WidgetTester tester) async {
      final controller = TextEditingController();

      // Test outlined variant (default)
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Outlined Field',
            controller: controller,
            variant: TextFieldVariant.outlined,
          ),
        ),
      );

      expect(find.byType(TextFormField), findsOneWidget);

      // Test filled variant
      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Filled Field',
            controller: controller,
            variant: TextFieldVariant.filled,
          ),
        ),
      );

      expect(find.byType(TextFormField), findsOneWidget);

      controller.dispose();
    });

    testWidgets('should handle focus correctly', (WidgetTester tester) async {
      final controller = TextEditingController();
      final focusNode = FocusNode();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: CustomTextField(
            label: 'Focus Field',
            controller: controller,
            focusNode: focusNode,
          ),
        ),
      );

      // Request focus
      focusNode.requestFocus();
      await tester.pumpAndSettle();

      expect(focusNode.hasFocus, isTrue);

      controller.dispose();
      focusNode.dispose();
    });

    testWidgets('should handle validation correctly',
        (WidgetTester tester) async {
      final controller = TextEditingController();
      final formKey = GlobalKey<FormState>();

      await tester.pumpWidget(
        TestHelpers.createTestWidget(
          child: Form(
            key: formKey,
            child: CustomTextField(
              label: 'Validation Field',
              controller: controller,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'This field is required';
                }
                return null;
              },
            ),
          ),
        ),
      );

      // Validate empty field
      expect(formKey.currentState!.validate(), isFalse);
      await tester.pumpAndSettle();

      expect(find.text('This field is required'), findsOneWidget);

      // Enter valid text
      await tester.enterText(find.byType(TextFormField), 'Valid text');
      expect(formKey.currentState!.validate(), isTrue);

      controller.dispose();
    });
  });
}
