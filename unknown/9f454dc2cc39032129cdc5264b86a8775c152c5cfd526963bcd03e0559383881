/// Security configuration constants and settings
class SecurityConfig {
  // Encryption settings
  static const int encryptionKeyLength = 32; // 256 bits
  static const int saltLength = 16; // 128 bits
  static const int ivLength = 12; // 96 bits for GCM
  static const int tagLength = 16; // 128 bits for GCM

  // Authentication settings
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const Duration sessionTimeout = Duration(hours: 24);
  static const Duration biometricTimeout = Duration(minutes: 5);

  // Password requirements
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const bool requireUppercase = true;
  static const bool requireLowercase = true;
  static const bool requireNumbers = true;
  static const bool requireSpecialChars = true;

  // PIN requirements
  static const int pinLength = 6;
  static const List<String> weakPins = [
    '000000',
    '111111',
    '222222',
    '333333',
    '444444',
    '555555',
    '666666',
    '777777',
    '888888',
    '999999',
    '123456',
    '654321',
    '012345',
    '543210',
  ];

  // Transaction limits
  static const double maxTransactionAmount = 5000.0;
  static const double maxDailyAmount = 10000.0;
  static const int maxTransactionsPerHour = 10;
  static const int maxTransactionsPerDay = 50;

  // Fraud detection thresholds
  static const double lowRiskThreshold = 0.3;
  static const double mediumRiskThreshold = 0.6;
  static const double highRiskThreshold = 0.8;
  static const double blockThreshold = 0.9;

  // High-risk merchant categories
  static const List<String> highRiskMerchantCategories = [
    'gambling',
    'adult_entertainment',
    'cryptocurrency',
    'money_transfer',
    'cash_advance',
    'payday_loans',
    'debt_collection',
    'political_organizations',
  ];

  // Medium-risk merchant categories
  static const List<String> mediumRiskMerchantCategories = [
    'online_gaming',
    'digital_goods',
    'subscription_services',
    'dating_services',
    'travel_agencies',
    'car_rental',
    'hotels_motels',
  ];

  // Network security settings
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  // Certificate pinning (production domains)
  static const List<String> pinnedDomains = [
    'api.potto.app',
    'stripe.com',
    'api.stripe.com',
    'supabase.co',
  ];

  // Security headers
  static const Map<String, String> securityHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'",
    'Referrer-Policy': 'strict-origin-when-cross-origin',
  };

  // Input validation patterns
  static const Map<String, String> validationPatterns = {
    'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    'phone': r'^\+?[1-9]\d{1,14}$',
    'creditCard': r'^[0-9]{13,19}$',
    'cvv': r'^[0-9]{3,4}$',
    'expiryDate': r'^(0[1-9]|1[0-2])\/([0-9]{2})$',
    'name': r"^[a-zA-Z\s\-\.']{2,50}$",
    'walletName': r'^[a-zA-Z0-9\s\-_]{3,50}$',
    'amount': r'^\d+(\.\d{1,2})?$',
    'pin': r'^[0-9]{4,6}$',
  };

  // SQL injection patterns
  static const List<String> sqlInjectionPatterns = [
    r'(union|select|insert|update|delete|drop|create|alter)\s+',
    '[\'";].*(-{2}|/\\*|\\*/)',
    r'(exec|execute|sp_|xp_)',
    r'(script|javascript|vbscript)',
    r'(\bor\b|\band\b)\s+\d+\s*=\s*\d+',
    '(\\bor\\b|\\band\\b)\\s+[\'"].*[\'"]',
  ];

  // XSS patterns
  static const List<String> xssPatterns = [
    r'<script[^>]*>.*?</script>',
    r'javascript:',
    r'on\w+\s*=',
    r'<iframe[^>]*>.*?</iframe>',
    r'<object[^>]*>.*?</object>',
    r'<embed[^>]*>.*?</embed>',
    r'<link[^>]*>.*?</link>',
    r'<meta[^>]*>.*?</meta>',
    r'expression\s*\(',
    r'url\s*\(',
    r'@import',
  ];

  // Dangerous file extensions
  static const List<String> dangerousFileExtensions = [
    'exe',
    'bat',
    'cmd',
    'com',
    'pif',
    'scr',
    'vbs',
    'js',
    'jar',
    'app',
    'deb',
    'pkg',
    'dmg',
    'iso',
  ];

  // Allowed image extensions
  static const List<String> allowedImageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'svg',
  ];

  // Maximum file sizes (in bytes)
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxDocumentSize = 10 * 1024 * 1024; // 10MB

  // Rate limiting
  static const Map<String, int> rateLimits = {
    'login': 5, // per minute
    'register': 3, // per minute
    'password_reset': 3, // per hour
    'transaction': 10, // per minute
    'card_creation': 5, // per hour
    'invitation': 10, // per hour
  };

  // Audit log settings
  static const bool enableAuditLogging = true;
  static const Duration auditLogRetention = Duration(days: 90);
  static const List<String> auditEvents = [
    'login',
    'logout',
    'password_change',
    'profile_update',
    'transaction',
    'card_creation',
    'card_activation',
    'card_deactivation',
    'spending_limit_change',
    'invitation_sent',
    'invitation_accepted',
    'member_added',
    'member_removed',
    'role_changed',
    'permissions_changed',
  ];

  // Compliance settings
  static const bool pciComplianceMode = true;
  static const bool gdprComplianceMode = true;
  static const bool ccpaComplianceMode = true;

  // Data retention periods
  static const Duration transactionDataRetention =
      Duration(days: 2555); // 7 years
  static const Duration userDataRetention = Duration(days: 1095); // 3 years
  static const Duration logDataRetention = Duration(days: 365); // 1 year
  static const Duration sessionDataRetention = Duration(days: 30);

  // Encryption algorithms
  static const String symmetricAlgorithm = 'AES-256-GCM';
  static const String hashAlgorithm = 'SHA-256';
  static const String keyDerivationAlgorithm = 'PBKDF2';
  static const int keyDerivationIterations = 100000;

  // Biometric settings
  static const bool biometricFallbackEnabled = true;
  static const Duration biometricCacheTimeout = Duration(minutes: 5);
  static const int maxBiometricAttempts = 3;

  // Device security
  static const bool requireDeviceLock = true;
  static const bool detectJailbreak = true;
  static const bool detectDebugging = true;
  static const bool requireAppSignature = true;

  // Network security
  static const bool requireTLS = true;
  static const String minTLSVersion = '1.2';
  static const bool certificatePinningEnabled = true;
  static const bool allowSelfSignedCertificates = false;

  // API security
  static const bool requireAPIKey = true;
  static const bool requireRequestSigning = true;
  static const Duration requestTimestampTolerance = Duration(minutes: 5);
  static const bool enableRequestThrottling = true;

  // Security monitoring
  static const bool enableSecurityMonitoring = true;
  static const bool enableFraudDetection = true;
  static const bool enableAnomalyDetection = true;
  static const bool enableThreatIntelligence = true;

  // Emergency settings
  static const bool enableEmergencyMode = true;
  static const Duration emergencyLockoutDuration = Duration(hours: 24);
  static const int maxEmergencyAttempts = 3;

  // Validation methods
  static bool isValidEmail(String email) {
    return RegExp(validationPatterns['email']!).hasMatch(email);
  }

  static bool isValidPhone(String phone) {
    return RegExp(validationPatterns['phone']!).hasMatch(phone);
  }

  static bool isStrongPassword(String password) {
    if (password.length < minPasswordLength ||
        password.length > maxPasswordLength) {
      return false;
    }

    if (requireUppercase && !password.contains(RegExp(r'[A-Z]'))) {
      return false;
    }

    if (requireLowercase && !password.contains(RegExp(r'[a-z]'))) {
      return false;
    }

    if (requireNumbers && !password.contains(RegExp(r'[0-9]'))) {
      return false;
    }

    if (requireSpecialChars &&
        !password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return false;
    }

    return true;
  }

  static bool isWeakPin(String pin) {
    return weakPins.contains(pin);
  }

  static bool containsSQLInjection(String input) {
    for (final pattern in sqlInjectionPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return true;
      }
    }
    return false;
  }

  static bool containsXSS(String input) {
    for (final pattern in xssPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return true;
      }
    }
    return false;
  }

  static bool isDangerousFile(String filename) {
    final extension = filename.split('.').last.toLowerCase();
    return dangerousFileExtensions.contains(extension);
  }

  static bool isAllowedImageFile(String filename) {
    final extension = filename.split('.').last.toLowerCase();
    return allowedImageExtensions.contains(extension);
  }
}
