name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  FLUTTER_VERSION: '3.19.0'
  JAVA_VERSION: '17'
  XCODE_VERSION: '15.0'

jobs:
  # Code Quality and Analysis
  analyze:
    name: Code Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Generate code
        run: flutter packages pub run build_runner build --delete-conflicting-outputs
        
      - name: Analyze code
        run: flutter analyze --fatal-infos
        
      - name: Check formatting
        run: dart format --set-exit-if-changed .
        
      - name: Check for unused dependencies
        run: flutter pub deps

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Unit and Widget Tests
  test:
    name: Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [analyze]
    
    strategy:
      matrix:
        flutter-version: ['3.16.0', '3.19.0']
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ matrix.flutter-version }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Generate code
        run: flutter packages pub run build_runner build --delete-conflicting-outputs
        
      - name: Run tests with coverage
        run: |
          chmod +x scripts/run_tests.sh
          ./scripts/run_tests.sh --all-tests --coverage
          
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          
      - name: Archive test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-${{ matrix.flutter-version }}
          path: |
            test_results/
            coverage/
            
  # Integration Tests
  integration-test:
    name: Integration Tests
    runs-on: macos-latest
    timeout-minutes: 45
    needs: [test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Generate code
        run: flutter packages pub run build_runner build --delete-conflicting-outputs
        
      - name: Start iOS Simulator
        run: |
          xcrun simctl boot "iPhone 15"
          xcrun simctl list devices
          
      - name: Run integration tests
        run: |
          chmod +x scripts/run_tests.sh
          ./scripts/run_tests.sh --integration-tests
          
      - name: Archive integration test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: test_results/

  # Build Android
  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Generate code
        run: flutter packages pub run build_runner build --delete-conflicting-outputs
        
      - name: Build APK
        run: flutter build apk --release
        
      - name: Build App Bundle
        run: flutter build appbundle --release
        
      - name: Upload APK
        uses: actions/upload-artifact@v3
        with:
          name: android-apk
          path: build/app/outputs/flutter-apk/app-release.apk
          
      - name: Upload App Bundle
        uses: actions/upload-artifact@v3
        with:
          name: android-aab
          path: build/app/outputs/bundle/release/app-release.aab

  # Build iOS
  build-ios:
    name: Build iOS
    runs-on: macos-latest
    timeout-minutes: 45
    needs: [test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Generate code
        run: flutter packages pub run build_runner build --delete-conflicting-outputs
        
      - name: Build iOS (no codesign)
        run: flutter build ios --release --no-codesign
        
      - name: Archive iOS build
        uses: actions/upload-artifact@v3
        with:
          name: ios-build
          path: build/ios/iphoneos/Runner.app

  # Deploy to Firebase App Distribution (Development)
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [build-android, security]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download Android APK
        uses: actions/download-artifact@v3
        with:
          name: android-apk
          path: build/app/outputs/flutter-apk/
          
      - name: Deploy to Firebase App Distribution
        uses: wzieba/Firebase-Distribution-Github-Action@v1
        with:
          appId: ${{ secrets.FIREBASE_APP_ID_ANDROID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          groups: testers
          file: build/app/outputs/flutter-apk/app-release.apk
          releaseNotes: |
            Development build from commit ${{ github.sha }}
            Branch: ${{ github.ref_name }}

  # Deploy to Production
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [build-android, build-ios, integration-test, security]
    if: github.event_name == 'release'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download Android App Bundle
        uses: actions/download-artifact@v3
        with:
          name: android-aab
          path: build/app/outputs/bundle/release/
          
      - name: Deploy to Google Play Store
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.potto.app
          releaseFiles: build/app/outputs/bundle/release/app-release.aab
          track: production
          status: completed
          
      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false

  # Notify on completion
  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-prod]
    if: always()
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          channel: '#potto-deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
