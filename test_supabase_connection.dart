import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Load environment variables
    await dotenv.load(fileName: ".env");
    
    // Get Supabase credentials
    final supabaseUrl = dotenv.env['SUPABASE_URL'] ?? '';
    final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'] ?? '';
    
    print('Supabase URL: $supabaseUrl');
    print('Supabase Anon Key: ${supabaseAnonKey.substring(0, 20)}...');
    
    // Initialize Supabase
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
    );
    
    print('✅ Supabase initialized successfully!');
    
    // Test connection by checking if we can access the client
    final client = Supabase.instance.client;
    print('✅ Supabase client created successfully!');
    
    // Test a simple query (this will fail if tables don't exist yet, but connection is working)
    try {
      final response = await client.from('users').select('count').count();
      print('✅ Database connection successful! User count: ${response.count}');
    } catch (e) {
      print('⚠️  Database query failed (this is expected if tables aren\'t created yet): $e');
      print('✅ But Supabase connection is working!');
    }
    
  } catch (e) {
    print('❌ Supabase setup failed: $e');
    print('');
    print('Make sure you have:');
    print('1. Created a Supabase project');
    print('2. Updated your .env file with correct SUPABASE_URL and SUPABASE_ANON_KEY');
    print('3. The .env file is in the root of your project');
  }
}
