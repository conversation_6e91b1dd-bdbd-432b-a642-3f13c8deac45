import 'dart:io';

void main() async {
  try {
    // Read .env file directly
    final envFile = File('.env');
    if (!envFile.existsSync()) {
      print('❌ .env file not found!');
      return;
    }

    final envContent = await envFile.readAsString();
    final envLines = envContent.split('\n');

    String? supabaseUrl;
    String? supabaseAnonKey;

    for (final line in envLines) {
      if (line.startsWith('SUPABASE_URL=')) {
        supabaseUrl = line.split('=')[1];
      } else if (line.startsWith('SUPABASE_ANON_KEY=')) {
        supabaseAnonKey = line.split('=')[1];
      }
    }

    print('🔍 Checking Supabase Configuration...');
    print('');

    if (supabaseUrl == null ||
        supabaseUrl.isEmpty ||
        supabaseUrl == 'https://your-project-id.supabase.co') {
      print('❌ SUPABASE_URL not configured properly');
      print('   Current value: $supabaseUrl');
      print('   Expected format: https://your-project-id.supabase.co');
      return;
    }

    if (supabaseAnonKey == null ||
        supabaseAnonKey.isEmpty ||
        supabaseAnonKey == 'your_actual_anon_key_here') {
      print('❌ SUPABASE_ANON_KEY not configured properly');
      print('   Current value: ${supabaseAnonKey?.substring(0, 20)}...');
      return;
    }

    print('✅ SUPABASE_URL configured: $supabaseUrl');
    print(
        '✅ SUPABASE_ANON_KEY configured: ${supabaseAnonKey.substring(0, 20)}...');
    print('');

    // Test HTTP connection to Supabase
    print('🌐 Testing HTTP connection to Supabase...');
    final httpClient = HttpClient();

    try {
      final request =
          await httpClient.getUrl(Uri.parse('$supabaseUrl/rest/v1/'));
      request.headers.set('apikey', supabaseAnonKey);
      request.headers.set('Authorization', 'Bearer $supabaseAnonKey');

      final response = await request.close();

      if (response.statusCode == 200 || response.statusCode == 404) {
        print('✅ HTTP connection to Supabase successful!');
        print('   Status code: ${response.statusCode}');

        if (response.statusCode == 404) {
          print('   Note: 404 is expected if no tables exist yet');
        }
      } else {
        print('⚠️  Unexpected status code: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ HTTP connection failed: $e');
    } finally {
      httpClient.close();
    }

    print('');
    print('🎉 Supabase configuration test complete!');
    print('');
    print('Next steps:');
    print('1. Apply your database schema in Supabase dashboard');
    print('2. Set up Row Level Security policies');
    print('3. Configure authentication settings');
    print('4. Test your Flutter app');
  } catch (e) {
    print('❌ Test failed: $e');
  }
}
