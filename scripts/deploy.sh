#!/bin/bash

# Potto App Deployment Script
# This script handles deployment to different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DEPLOYMENT_CONFIG="$PROJECT_ROOT/deployment/config.yaml"
BUILD_DIR="$PROJECT_ROOT/build"
ARTIFACTS_DIR="$PROJECT_ROOT/deployment/artifacts"

# Default values
ENVIRONMENT="development"
PLATFORM="both"
BUILD_TYPE="release"
SKIP_TESTS=false
SKIP_BUILD=false
DRY_RUN=false
VERBOSE=false

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy Potto app to specified environment

OPTIONS:
    -e, --environment ENV    Target environment (development, staging, production)
    -p, --platform PLATFORM Target platform (android, ios, both)
    -t, --type TYPE         Build type (debug, release)
    --skip-tests            Skip running tests before deployment
    --skip-build            Skip building the app (use existing build)
    --dry-run               Show what would be deployed without actually deploying
    -v, --verbose           Enable verbose output
    -h, --help              Show this help message

EXAMPLES:
    $0 -e development -p android
    $0 -e staging -p both --skip-tests
    $0 -e production -p ios --dry-run
    
ENVIRONMENTS:
    development - Deploy to development environment with Firebase App Distribution
    staging     - Deploy to staging environment for beta testing
    production  - Deploy to production app stores
    
PLATFORMS:
    android - Build and deploy Android APK/AAB
    ios     - Build and deploy iOS IPA
    both    - Build and deploy both platforms
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Valid environments: development, staging, production"
    exit 1
fi

# Validate platform
if [[ ! "$PLATFORM" =~ ^(android|ios|both)$ ]]; then
    print_error "Invalid platform: $PLATFORM"
    print_error "Valid platforms: android, ios, both"
    exit 1
fi

# Validate build type
if [[ ! "$BUILD_TYPE" =~ ^(debug|release)$ ]]; then
    print_error "Invalid build type: $BUILD_TYPE"
    print_error "Valid build types: debug, release"
    exit 1
fi

# Function to check prerequisites
check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check Flutter
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Check Flutter version
    FLUTTER_VERSION=$(flutter --version | head -n 1 | cut -d ' ' -f 2)
    print_info "Flutter version: $FLUTTER_VERSION"
    
    # Check if we're in a Flutter project
    if [[ ! -f "$PROJECT_ROOT/pubspec.yaml" ]]; then
        print_error "Not in a Flutter project directory"
        exit 1
    fi
    
    # Check platform-specific tools
    if [[ "$PLATFORM" == "android" || "$PLATFORM" == "both" ]]; then
        if ! command -v java &> /dev/null; then
            print_error "Java is not installed or not in PATH"
            exit 1
        fi
    fi
    
    if [[ "$PLATFORM" == "ios" || "$PLATFORM" == "both" ]]; then
        if [[ "$OSTYPE" != "darwin"* ]]; then
            print_error "iOS builds require macOS"
            exit 1
        fi
        
        if ! command -v xcodebuild &> /dev/null; then
            print_error "Xcode is not installed or not in PATH"
            exit 1
        fi
    fi
    
    print_success "Prerequisites check passed"
}

# Function to setup environment
setup_environment() {
    print_step "Setting up environment for $ENVIRONMENT..."
    
    # Create artifacts directory
    mkdir -p "$ARTIFACTS_DIR"
    
    # Load environment-specific configuration
    case $ENVIRONMENT in
        development)
            export FLUTTER_ENV="development"
            export APP_SUFFIX=".dev"
            ;;
        staging)
            export FLUTTER_ENV="staging"
            export APP_SUFFIX=".staging"
            ;;
        production)
            export FLUTTER_ENV="production"
            export APP_SUFFIX=""
            ;;
    esac
    
    print_info "Environment: $ENVIRONMENT"
    print_info "App suffix: $APP_SUFFIX"
    
    print_success "Environment setup complete"
}

# Function to run tests
run_tests() {
    if $SKIP_TESTS; then
        print_warning "Skipping tests as requested"
        return 0
    fi
    
    print_step "Running tests..."
    
    # Make test script executable
    chmod +x "$PROJECT_ROOT/scripts/run_tests.sh"
    
    # Run tests based on environment
    case $ENVIRONMENT in
        development)
            "$PROJECT_ROOT/scripts/run_tests.sh" --unit-tests --widget-tests
            ;;
        staging)
            "$PROJECT_ROOT/scripts/run_tests.sh" --all-tests --no-coverage
            ;;
        production)
            "$PROJECT_ROOT/scripts/run_tests.sh" --all-tests --coverage
            ;;
    esac
    
    if [[ $? -ne 0 ]]; then
        print_error "Tests failed. Deployment aborted."
        exit 1
    fi
    
    print_success "All tests passed"
}

# Function to get dependencies
get_dependencies() {
    print_step "Getting dependencies..."
    
    flutter pub get
    
    if [[ $? -ne 0 ]]; then
        print_error "Failed to get dependencies"
        exit 1
    fi
    
    print_success "Dependencies updated"
}

# Function to generate code
generate_code() {
    print_step "Generating code..."
    
    flutter packages pub run build_runner build --delete-conflicting-outputs
    
    if [[ $? -ne 0 ]]; then
        print_error "Code generation failed"
        exit 1
    fi
    
    print_success "Code generation complete"
}

# Function to build Android
build_android() {
    print_step "Building Android ($BUILD_TYPE)..."
    
    if $SKIP_BUILD; then
        print_warning "Skipping Android build as requested"
        return 0
    fi
    
    # Build APK for development/staging, AAB for production
    if [[ "$ENVIRONMENT" == "production" ]]; then
        print_info "Building Android App Bundle for production..."
        flutter build appbundle --$BUILD_TYPE --dart-define=FLUTTER_ENV=$FLUTTER_ENV
        
        # Copy AAB to artifacts
        cp "$BUILD_DIR/app/outputs/bundle/${BUILD_TYPE}/app-${BUILD_TYPE}.aab" \
           "$ARTIFACTS_DIR/potto-${ENVIRONMENT}-${BUILD_TYPE}.aab"
    else
        print_info "Building Android APK for $ENVIRONMENT..."
        flutter build apk --$BUILD_TYPE --dart-define=FLUTTER_ENV=$FLUTTER_ENV
        
        # Copy APK to artifacts
        cp "$BUILD_DIR/app/outputs/flutter-apk/app-${BUILD_TYPE}.apk" \
           "$ARTIFACTS_DIR/potto-${ENVIRONMENT}-${BUILD_TYPE}.apk"
    fi
    
    if [[ $? -ne 0 ]]; then
        print_error "Android build failed"
        exit 1
    fi
    
    print_success "Android build complete"
}

# Function to build iOS
build_ios() {
    print_step "Building iOS ($BUILD_TYPE)..."
    
    if $SKIP_BUILD; then
        print_warning "Skipping iOS build as requested"
        return 0
    fi
    
    # Build iOS
    if [[ "$BUILD_TYPE" == "release" ]]; then
        flutter build ios --release --dart-define=FLUTTER_ENV=$FLUTTER_ENV
    else
        flutter build ios --debug --dart-define=FLUTTER_ENV=$FLUTTER_ENV
    fi
    
    if [[ $? -ne 0 ]]; then
        print_error "iOS build failed"
        exit 1
    fi
    
    print_success "iOS build complete"
}

# Function to deploy to development
deploy_development() {
    print_step "Deploying to development environment..."
    
    if $DRY_RUN; then
        print_info "[DRY RUN] Would deploy to Firebase App Distribution"
        return 0
    fi
    
    # Deploy Android to Firebase App Distribution
    if [[ "$PLATFORM" == "android" || "$PLATFORM" == "both" ]]; then
        print_info "Deploying Android to Firebase App Distribution..."
        # Add Firebase CLI deployment command here
        print_warning "Firebase App Distribution deployment not implemented yet"
    fi
    
    # Deploy iOS to TestFlight
    if [[ "$PLATFORM" == "ios" || "$PLATFORM" == "both" ]]; then
        print_info "Deploying iOS to TestFlight..."
        # Add TestFlight deployment command here
        print_warning "TestFlight deployment not implemented yet"
    fi
    
    print_success "Development deployment complete"
}

# Function to deploy to staging
deploy_staging() {
    print_step "Deploying to staging environment..."
    
    if $DRY_RUN; then
        print_info "[DRY RUN] Would deploy to staging environment"
        return 0
    fi
    
    # Similar to development but with different groups/tracks
    print_warning "Staging deployment not implemented yet"
    print_success "Staging deployment complete"
}

# Function to deploy to production
deploy_production() {
    print_step "Deploying to production environment..."
    
    if $DRY_RUN; then
        print_info "[DRY RUN] Would deploy to production app stores"
        return 0
    fi
    
    # Deploy to Google Play Store and App Store
    print_warning "Production deployment not implemented yet"
    print_success "Production deployment complete"
}

# Function to create deployment summary
create_summary() {
    print_step "Creating deployment summary..."
    
    local summary_file="$ARTIFACTS_DIR/deployment-summary-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$summary_file" << EOF
Potto App Deployment Summary
============================

Deployment Date: $(date)
Environment: $ENVIRONMENT
Platform: $PLATFORM
Build Type: $BUILD_TYPE

Configuration:
- Skip Tests: $SKIP_TESTS
- Skip Build: $SKIP_BUILD
- Dry Run: $DRY_RUN

Artifacts:
$(ls -la "$ARTIFACTS_DIR" | grep -E '\.(apk|aab|ipa)$' || echo "No build artifacts found")

Git Information:
- Branch: $(git branch --show-current 2>/dev/null || echo "Unknown")
- Commit: $(git rev-parse HEAD 2>/dev/null || echo "Unknown")
- Status: $(git status --porcelain 2>/dev/null | wc -l | tr -d ' ') uncommitted changes

Flutter Information:
- Flutter Version: $(flutter --version | head -n 1)
- Dart Version: $(dart --version)

EOF
    
    print_info "Deployment summary saved to: $summary_file"
}

# Main deployment function
main() {
    print_info "Starting Potto app deployment..."
    print_info "Environment: $ENVIRONMENT"
    print_info "Platform: $PLATFORM"
    print_info "Build Type: $BUILD_TYPE"
    
    if $DRY_RUN; then
        print_warning "DRY RUN MODE - No actual deployment will occur"
    fi
    
    # Execute deployment steps
    check_prerequisites
    setup_environment
    run_tests
    get_dependencies
    generate_code
    
    # Build for specified platforms
    if [[ "$PLATFORM" == "android" || "$PLATFORM" == "both" ]]; then
        build_android
    fi
    
    if [[ "$PLATFORM" == "ios" || "$PLATFORM" == "both" ]]; then
        build_ios
    fi
    
    # Deploy to specified environment
    case $ENVIRONMENT in
        development)
            deploy_development
            ;;
        staging)
            deploy_staging
            ;;
        production)
            deploy_production
            ;;
    esac
    
    create_summary
    
    print_success "Deployment completed successfully!"
    print_info "Check artifacts in: $ARTIFACTS_DIR"
}

# Run main function
main "$@"
