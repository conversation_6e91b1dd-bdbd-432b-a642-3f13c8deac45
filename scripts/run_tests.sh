#!/bin/bash

# Potto App Test Runner Script
# This script runs all tests with proper setup and cleanup

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_RESULTS_DIR="$PROJECT_ROOT/test_results"
COVERAGE_DIR="$PROJECT_ROOT/coverage"

# Default values
RUN_UNIT_TESTS=true
RUN_WIDGET_TESTS=true
RUN_INTEGRATION_TESTS=false
RUN_SECURITY_TESTS=true
GENERATE_COVERAGE=true
VERBOSE=false
CLEAN_FIRST=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -u, --unit-tests         Run unit tests only"
    echo "  -w, --widget-tests       Run widget tests only"
    echo "  -i, --integration-tests  Run integration tests only"
    echo "  -s, --security-tests     Run security tests only"
    echo "  -a, --all-tests          Run all tests (default)"
    echo "  -c, --coverage           Generate coverage report"
    echo "  --no-coverage            Skip coverage generation"
    echo "  --clean                  Clean before running tests"
    echo "  -v, --verbose            Verbose output"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                       # Run all tests with coverage"
    echo "  $0 -u                    # Run only unit tests"
    echo "  $0 -w -c                 # Run widget tests with coverage"
    echo "  $0 --clean -a            # Clean and run all tests"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--unit-tests)
            RUN_UNIT_TESTS=true
            RUN_WIDGET_TESTS=false
            RUN_INTEGRATION_TESTS=false
            RUN_SECURITY_TESTS=false
            shift
            ;;
        -w|--widget-tests)
            RUN_UNIT_TESTS=false
            RUN_WIDGET_TESTS=true
            RUN_INTEGRATION_TESTS=false
            RUN_SECURITY_TESTS=false
            shift
            ;;
        -i|--integration-tests)
            RUN_UNIT_TESTS=false
            RUN_WIDGET_TESTS=false
            RUN_INTEGRATION_TESTS=true
            RUN_SECURITY_TESTS=false
            shift
            ;;
        -s|--security-tests)
            RUN_UNIT_TESTS=false
            RUN_WIDGET_TESTS=false
            RUN_INTEGRATION_TESTS=false
            RUN_SECURITY_TESTS=true
            shift
            ;;
        -a|--all-tests)
            RUN_UNIT_TESTS=true
            RUN_WIDGET_TESTS=true
            RUN_INTEGRATION_TESTS=true
            RUN_SECURITY_TESTS=true
            shift
            ;;
        -c|--coverage)
            GENERATE_COVERAGE=true
            shift
            ;;
        --no-coverage)
            GENERATE_COVERAGE=false
            shift
            ;;
        --clean)
            CLEAN_FIRST=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Function to setup test environment
setup_test_environment() {
    print_status "Setting up test environment..."
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    mkdir -p "$COVERAGE_DIR"
    
    # Ensure we're in the project root
    cd "$PROJECT_ROOT"
    
    # Check if Flutter is installed
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    # Check Flutter version
    print_status "Flutter version:"
    flutter --version
    
    # Get dependencies
    print_status "Getting dependencies..."
    flutter pub get
    
    # Generate code if needed
    if [ -f "pubspec.yaml" ] && grep -q "build_runner" pubspec.yaml; then
        print_status "Generating code..."
        flutter packages pub run build_runner build --delete-conflicting-outputs
    fi
    
    print_success "Test environment setup complete"
}

# Function to clean project
clean_project() {
    print_status "Cleaning project..."
    
    flutter clean
    flutter pub get
    
    # Clean test results
    rm -rf "$TEST_RESULTS_DIR"
    rm -rf "$COVERAGE_DIR"
    
    print_success "Project cleaned"
}

# Function to run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    
    local test_files=(
        "test/unit/services/security_service_test.dart"
        "test/unit/services/fraud_prevention_service_test.dart"
        "test/unit/utils/validation_utils_test.dart"
    )
    
    local failed_tests=()
    
    for test_file in "${test_files[@]}"; do
        if [ -f "$test_file" ]; then
            print_status "Running $test_file..."
            if $VERBOSE; then
                flutter test "$test_file" --reporter=expanded
            else
                flutter test "$test_file"
            fi
            
            if [ $? -ne 0 ]; then
                failed_tests+=("$test_file")
            fi
        else
            print_warning "Test file not found: $test_file"
        fi
    done
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_success "All unit tests passed"
        return 0
    else
        print_error "Failed unit tests: ${failed_tests[*]}"
        return 1
    fi
}

# Function to run widget tests
run_widget_tests() {
    print_status "Running widget tests..."
    
    local test_files=(
        "test/widget/components/custom_button_test.dart"
        "test/widget/components/custom_text_field_test.dart"
    )
    
    local failed_tests=()
    
    for test_file in "${test_files[@]}"; do
        if [ -f "$test_file" ]; then
            print_status "Running $test_file..."
            if $VERBOSE; then
                flutter test "$test_file" --reporter=expanded
            else
                flutter test "$test_file"
            fi
            
            if [ $? -ne 0 ]; then
                failed_tests+=("$test_file")
            fi
        else
            print_warning "Test file not found: $test_file"
        fi
    done
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_success "All widget tests passed"
        return 0
    else
        print_error "Failed widget tests: ${failed_tests[*]}"
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    local test_files=(
        "test/integration/auth_flow_test.dart"
    )
    
    local failed_tests=()
    
    for test_file in "${test_files[@]}"; do
        if [ -f "$test_file" ]; then
            print_status "Running $test_file..."
            if $VERBOSE; then
                flutter test "$test_file" --reporter=expanded
            else
                flutter test "$test_file"
            fi
            
            if [ $? -ne 0 ]; then
                failed_tests+=("$test_file")
            fi
        else
            print_warning "Test file not found: $test_file"
        fi
    done
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_success "All integration tests passed"
        return 0
    else
        print_error "Failed integration tests: ${failed_tests[*]}"
        return 1
    fi
}

# Function to run security tests
run_security_tests() {
    print_status "Running security tests..."
    
    # Run security-related unit tests
    local security_test_pattern="test/unit/services/security_service_test.dart test/unit/services/fraud_prevention_service_test.dart test/unit/utils/validation_utils_test.dart"
    
    if $VERBOSE; then
        flutter test $security_test_pattern --reporter=expanded
    else
        flutter test $security_test_pattern
    fi
    
    if [ $? -eq 0 ]; then
        print_success "All security tests passed"
        return 0
    else
        print_error "Some security tests failed"
        return 1
    fi
}

# Function to run BLoC tests
run_bloc_tests() {
    print_status "Running BLoC tests..."
    
    local test_files=(
        "test/bloc/auth/auth_bloc_test.dart"
    )
    
    local failed_tests=()
    
    for test_file in "${test_files[@]}"; do
        if [ -f "$test_file" ]; then
            print_status "Running $test_file..."
            if $VERBOSE; then
                flutter test "$test_file" --reporter=expanded
            else
                flutter test "$test_file"
            fi
            
            if [ $? -ne 0 ]; then
                failed_tests+=("$test_file")
            fi
        else
            print_warning "Test file not found: $test_file"
        fi
    done
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_success "All BLoC tests passed"
        return 0
    else
        print_error "Failed BLoC tests: ${failed_tests[*]}"
        return 1
    fi
}

# Function to generate coverage report
generate_coverage() {
    print_status "Generating coverage report..."
    
    # Run tests with coverage
    flutter test --coverage
    
    # Check if lcov is installed
    if command -v lcov &> /dev/null; then
        # Generate HTML coverage report
        genhtml coverage/lcov.info -o coverage/html
        print_success "Coverage report generated at coverage/html/index.html"
    else
        print_warning "lcov not installed. Install with: sudo apt-get install lcov (Linux) or brew install lcov (macOS)"
        print_success "Coverage data available at coverage/lcov.info"
    fi
    
    # Show coverage summary
    if command -v lcov &> /dev/null; then
        lcov --summary coverage/lcov.info
    fi
}

# Function to cleanup after tests
cleanup() {
    print_status "Cleaning up..."
    
    # Kill any remaining processes
    pkill -f "flutter_tools.snapshot" || true
    
    print_success "Cleanup complete"
}

# Main execution
main() {
    print_status "Starting Potto App Test Suite"
    print_status "Project root: $PROJECT_ROOT"
    
    # Setup trap for cleanup
    trap cleanup EXIT
    
    # Clean if requested
    if $CLEAN_FIRST; then
        clean_project
    fi
    
    # Setup test environment
    setup_test_environment
    
    local test_results=()
    local overall_result=0
    
    # Run selected test suites
    if $RUN_UNIT_TESTS; then
        if run_unit_tests; then
            test_results+=("Unit tests: PASSED")
        else
            test_results+=("Unit tests: FAILED")
            overall_result=1
        fi
    fi
    
    if $RUN_WIDGET_TESTS; then
        if run_widget_tests; then
            test_results+=("Widget tests: PASSED")
        else
            test_results+=("Widget tests: FAILED")
            overall_result=1
        fi
    fi
    
    # Always run BLoC tests if running unit tests
    if $RUN_UNIT_TESTS; then
        if run_bloc_tests; then
            test_results+=("BLoC tests: PASSED")
        else
            test_results+=("BLoC tests: FAILED")
            overall_result=1
        fi
    fi
    
    if $RUN_INTEGRATION_TESTS; then
        if run_integration_tests; then
            test_results+=("Integration tests: PASSED")
        else
            test_results+=("Integration tests: FAILED")
            overall_result=1
        fi
    fi
    
    if $RUN_SECURITY_TESTS; then
        if run_security_tests; then
            test_results+=("Security tests: PASSED")
        else
            test_results+=("Security tests: FAILED")
            overall_result=1
        fi
    fi
    
    # Generate coverage if requested
    if $GENERATE_COVERAGE; then
        generate_coverage
    fi
    
    # Print summary
    echo ""
    print_status "Test Results Summary:"
    for result in "${test_results[@]}"; do
        if [[ $result == *"PASSED"* ]]; then
            print_success "$result"
        else
            print_error "$result"
        fi
    done
    
    echo ""
    if [ $overall_result -eq 0 ]; then
        print_success "All tests completed successfully!"
    else
        print_error "Some tests failed. Check the output above for details."
    fi
    
    exit $overall_result
}

# Run main function
main "$@"
