# App Store Metadata Configuration
# This file contains metadata for app store submissions

# General App Information
app_info:
  name: "Potto"
  subtitle: "Group Spending Made Simple"
  bundle_id: "com.potto.app"
  sku: "potto-app-2024"
  
  # Version Information
  version: "1.0.0"
  build_number: "1"
  
  # App Category
  primary_category: "Finance"
  secondary_category: "Social Networking"
  
  # Content Rating
  content_rating: "4+" # 4+, 9+, 12+, 17+
  
  # Pricing
  price_tier: "Free"
  
# App Store Description
description:
  # Short Description (30 characters max for iOS subtitle)
  short: "Group spending made simple"
  
  # Full Description
  full: |
    Potto is the modern way to manage group spending. Whether you're sharing expenses with roommates, planning a trip with friends, or managing family finances, <PERSON><PERSON> makes it effortless.
    
    KEY FEATURES:
    
    🏦 Virtual Group Cards
    • Create shared virtual cards for group expenses
    • Set spending limits and controls
    • Real-time transaction notifications
    • Secure Stripe-powered payments
    
    💰 Smart Expense Tracking
    • Automatic expense categorization
    • Real-time balance updates
    • Detailed spending analytics
    • Export transaction history
    
    👥 Easy Group Management
    • Invite members with simple links
    • Set member permissions and roles
    • Track individual contributions
    • Transparent spending history
    
    🔒 Bank-Level Security
    • End-to-end encryption
    • Biometric authentication
    • PCI DSS compliant
    • Fraud prevention built-in
    
    ⚡ Real-Time Updates
    • Instant transaction notifications
    • Live balance synchronization
    • Group chat integration
    • Activity feed updates
    
    Perfect for:
    • Roommate expenses
    • Travel groups
    • Family budgets
    • Team lunches
    • Event planning
    • Shared subscriptions
    
    Join thousands of users who trust Potto for their group spending needs. Download now and experience the future of shared finances!
  
  # What's New (for updates)
  whats_new: |
    Welcome to Potto 1.0! 🎉
    
    This is our initial release featuring:
    • Complete group wallet management
    • Virtual card creation and controls
    • Real-time expense tracking
    • Secure payment processing
    • Intuitive user interface
    
    We're excited to help you manage your group spending!

# Keywords and Search Optimization
seo:
  keywords:
    - "group spending"
    - "shared expenses"
    - "virtual cards"
    - "expense tracking"
    - "split bills"
    - "group wallet"
    - "shared finances"
    - "expense sharing"
    - "group payments"
    - "financial management"
  
  # App Store Search Terms (100 characters max)
  search_terms: "group spending,shared expenses,virtual cards,split bills,expense tracking,group wallet"

# App Store Screenshots and Media
media:
  # Screenshot Categories
  screenshots:
    iphone_6_5:
      - "screenshots/iphone-6.5/01-home.png"
      - "screenshots/iphone-6.5/02-wallet.png"
      - "screenshots/iphone-6.5/03-transactions.png"
      - "screenshots/iphone-6.5/04-cards.png"
      - "screenshots/iphone-6.5/05-profile.png"
    
    iphone_5_5:
      - "screenshots/iphone-5.5/01-home.png"
      - "screenshots/iphone-5.5/02-wallet.png"
      - "screenshots/iphone-5.5/03-transactions.png"
      - "screenshots/iphone-5.5/04-cards.png"
      - "screenshots/iphone-5.5/05-profile.png"
    
    ipad_pro_12_9:
      - "screenshots/ipad-12.9/01-home.png"
      - "screenshots/ipad-12.9/02-wallet.png"
      - "screenshots/ipad-12.9/03-transactions.png"
    
    android_phone:
      - "screenshots/android/01-home.png"
      - "screenshots/android/02-wallet.png"
      - "screenshots/android/03-transactions.png"
      - "screenshots/android/04-cards.png"
      - "screenshots/android/05-profile.png"
    
    android_tablet:
      - "screenshots/android-tablet/01-home.png"
      - "screenshots/android-tablet/02-wallet.png"
      - "screenshots/android-tablet/03-transactions.png"
  
  # App Preview Videos
  app_previews:
    iphone: "previews/iphone-preview.mp4"
    ipad: "previews/ipad-preview.mp4"
    android: "previews/android-preview.mp4"
  
  # App Icon
  app_icon: "icons/app-icon-1024.png"
  
  # Feature Graphic (Google Play)
  feature_graphic: "graphics/feature-graphic.png"

# App Store Review Information
review_info:
  # Contact Information
  contact:
    first_name: "Potto"
    last_name: "Team"
    phone: "******-POTTO-APP"
    email: "<EMAIL>"
  
  # Demo Account (if required)
  demo_account:
    username: "<EMAIL>"
    password: "DemoPassword123!"
    notes: |
      Demo account for app review purposes.
      This account has sample data including:
      - Test group wallets
      - Sample transactions
      - Virtual cards (test mode)
      
      Please note that all payment functionality uses Stripe test mode.
  
  # Review Notes
  notes: |
    Thank you for reviewing Potto!
    
    IMPORTANT NOTES:
    • All payment processing uses Stripe in test mode for review
    • Biometric authentication may prompt during testing
    • Push notifications require permission approval
    • Location services are not used by the app
    
    KEY FEATURES TO TEST:
    1. Create a new group wallet
    2. Invite members (use demo emails)
    3. Create a virtual card
    4. Make a test transaction
    5. View transaction history and analytics
    
    If you have any questions, please contact our <NAME_EMAIL>

# Privacy and Compliance
privacy:
  # Privacy Policy URL
  privacy_policy_url: "https://potto.app/privacy"
  
  # Terms of Service URL
  terms_of_service_url: "https://potto.app/terms"
  
  # Data Collection
  data_collection:
    collects_data: true
    data_types:
      - "Financial Information"
      - "Contact Information"
      - "Usage Data"
      - "Device Information"
    
    data_usage:
      - "App Functionality"
      - "Analytics"
      - "Fraud Prevention"
      - "Customer Support"
    
    data_sharing:
      - "Payment Processors (Stripe)"
      - "Analytics Providers"
      - "Cloud Storage (Supabase)"
  
  # Age Rating Details
  age_rating:
    rating: "4+"
    content_descriptors: []
    
# Localization
localization:
  default_language: "en-US"
  supported_languages:
    - "en-US" # English (US)
    - "en-GB" # English (UK)
    - "es-ES" # Spanish (Spain)
    - "es-MX" # Spanish (Mexico)
    - "fr-FR" # French (France)
    - "de-DE" # German (Germany)
    - "it-IT" # Italian (Italy)
    - "pt-BR" # Portuguese (Brazil)
    - "ja-JP" # Japanese (Japan)
    - "ko-KR" # Korean (South Korea)
    - "zh-CN" # Chinese (Simplified)
    - "zh-TW" # Chinese (Traditional)

# Platform-Specific Configuration
platforms:
  # iOS App Store
  ios:
    # App Store Connect Information
    app_store_connect:
      team_id: "YOUR_TEAM_ID"
      app_id: "YOUR_APP_ID"
      bundle_id: "com.potto.app"
    
    # iOS Specific Settings
    settings:
      supports_iphone: true
      supports_ipad: true
      supports_mac: false
      requires_full_screen: false
      supports_game_center: false
      
    # iOS Capabilities
    capabilities:
      - "In-App Purchase"
      - "Push Notifications"
      - "Background App Refresh"
      - "Face ID / Touch ID"
      
  # Google Play Store
  android:
    # Google Play Console Information
    play_console:
      package_name: "com.potto.app"
      app_id: "YOUR_GOOGLE_PLAY_APP_ID"
      
    # Android Specific Settings
    settings:
      supports_phone: true
      supports_tablet: true
      supports_tv: false
      supports_wear: false
      
    # Google Play Features
    features:
      - "In-app billing"
      - "Cloud messaging"
      - "Biometric authentication"
      
    # Content Rating (ESRB)
    content_rating:
      rating: "Everyone"
      interactive_elements: []

# Release Configuration
release:
  # Release Type
  type: "major" # major, minor, patch
  
  # Release Notes Template
  notes_template: |
    🎉 Potto v{version} is here!
    
    {whats_new}
    
    As always, we're committed to making group spending simple and secure. 
    
    Questions or feedback? Reach out to <NAME_EMAIL>
    
    Happy spending! 💳✨
  
  # Rollout Configuration
  rollout:
    staged_rollout: true
    initial_percentage: 5
    rollout_schedule:
      - percentage: 5
        duration: "24h"
      - percentage: 20
        duration: "48h"
      - percentage: 50
        duration: "72h"
      - percentage: 100
        
  # Post-Release Monitoring
  monitoring:
    crash_threshold: 2.0 # percentage
    rating_threshold: 4.0 # minimum rating
    review_monitoring: true
