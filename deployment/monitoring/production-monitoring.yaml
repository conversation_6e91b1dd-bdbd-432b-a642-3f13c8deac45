# Production Monitoring Configuration for Potto App
# This file defines monitoring, alerting, and observability settings

# Application Performance Monitoring (APM)
apm:
  # Firebase Performance Monitoring
  firebase_performance:
    enabled: true
    sample_rate: 0.1 # 10% of sessions
    
    # Custom Traces
    custom_traces:
      - name: "app_startup"
        description: "Time from app launch to first screen"
        
      - name: "wallet_creation"
        description: "Time to create a new wallet"
        
      - name: "payment_processing"
        description: "Time to process a payment"
        
      - name: "card_issuance"
        description: "Time to issue a virtual card"
        
    # Network Request Monitoring
    network_monitoring:
      enabled: true
      url_patterns:
        - "https://api.stripe.com/*"
        - "https://*.supabase.co/*"
        - "https://api.potto.app/*"
        
  # Custom Performance Metrics
  custom_metrics:
    # Business Metrics
    business_metrics:
      - name: "daily_active_users"
        type: "gauge"
        description: "Number of unique users per day"
        
      - name: "wallet_creation_rate"
        type: "counter"
        description: "Rate of new wallet creation"
        
      - name: "transaction_volume"
        type: "histogram"
        description: "Distribution of transaction amounts"
        
      - name: "payment_success_rate"
        type: "counter"
        description: "Percentage of successful payments"
        
    # Technical Metrics
    technical_metrics:
      - name: "api_response_time"
        type: "histogram"
        description: "API response time distribution"
        
      - name: "database_query_time"
        type: "histogram"
        description: "Database query performance"
        
      - name: "memory_usage"
        type: "gauge"
        description: "App memory consumption"
        
      - name: "battery_usage"
        type: "gauge"
        description: "Battery consumption rate"

# Error Tracking and Crash Reporting
error_tracking:
  # Firebase Crashlytics
  crashlytics:
    enabled: true
    
    # Crash Reporting Settings
    settings:
      collect_user_identifiers: true
      collect_custom_keys: true
      collect_custom_logs: true
      
    # Custom Keys to Track
    custom_keys:
      - "user_id"
      - "wallet_id"
      - "transaction_id"
      - "card_id"
      - "environment"
      - "app_version"
      - "flutter_version"
      
    # Fatal Error Thresholds
    thresholds:
      crash_free_rate: 99.5 # Minimum crash-free rate
      crash_velocity: 10 # Max crashes per hour
      
  # Custom Error Tracking
  custom_errors:
    # Payment Errors
    payment_errors:
      - "stripe_payment_failed"
      - "insufficient_funds"
      - "card_declined"
      - "payment_timeout"
      
    # Authentication Errors
    auth_errors:
      - "login_failed"
      - "biometric_auth_failed"
      - "token_expired"
      - "unauthorized_access"
      
    # Business Logic Errors
    business_errors:
      - "wallet_creation_failed"
      - "invitation_send_failed"
      - "member_add_failed"
      - "transaction_sync_failed"

# Real-time Monitoring and Alerting
alerting:
  # Alert Channels
  channels:
    # Slack Integration
    slack:
      enabled: true
      webhook_url_env: "SLACK_MONITORING_WEBHOOK"
      channels:
        critical: "#potto-critical-alerts"
        warning: "#potto-warnings"
        info: "#potto-monitoring"
        
    # Email Alerts
    email:
      enabled: true
      recipients:
        critical:
          - "<EMAIL>"
          - "<EMAIL>"
        warning:
          - "<EMAIL>"
          - "<EMAIL>"
        info:
          - "<EMAIL>"
          
    # PagerDuty Integration
    pagerduty:
      enabled: true
      integration_key_env: "PAGERDUTY_INTEGRATION_KEY"
      escalation_policy: "Potto Production Escalation"
      
    # Discord Integration
    discord:
      enabled: true
      webhook_url_env: "DISCORD_MONITORING_WEBHOOK"
      
  # Alert Rules
  rules:
    # Critical Alerts
    critical:
      - name: "app_crash_rate_high"
        condition: "crash_rate > 5%"
        duration: "5m"
        description: "App crash rate is above 5%"
        
      - name: "payment_failure_rate_high"
        condition: "payment_failure_rate > 10%"
        duration: "5m"
        description: "Payment failure rate is above 10%"
        
      - name: "api_error_rate_high"
        condition: "api_error_rate > 15%"
        duration: "5m"
        description: "API error rate is above 15%"
        
      - name: "database_connection_failed"
        condition: "database_connection_errors > 0"
        duration: "1m"
        description: "Database connection failures detected"
        
    # Warning Alerts
    warning:
      - name: "response_time_degraded"
        condition: "avg_response_time > 2s"
        duration: "10m"
        description: "Average response time is above 2 seconds"
        
      - name: "memory_usage_high"
        condition: "memory_usage > 80%"
        duration: "15m"
        description: "Memory usage is above 80%"
        
      - name: "user_rating_low"
        condition: "avg_rating < 4.0"
        duration: "1h"
        description: "Average user rating is below 4.0"
        
    # Info Alerts
    info:
      - name: "new_version_deployed"
        condition: "deployment_event"
        description: "New app version has been deployed"
        
      - name: "daily_metrics_summary"
        condition: "schedule:daily"
        description: "Daily metrics summary"

# Health Checks and Uptime Monitoring
health_checks:
  # Application Health Endpoints
  endpoints:
    - name: "app_health"
      url: "https://api.potto.app/health"
      method: "GET"
      timeout: 30
      interval: 60 # seconds
      expected_status: 200
      
    - name: "database_health"
      url: "https://api.potto.app/health/database"
      method: "GET"
      timeout: 15
      interval: 120
      expected_status: 200
      
    - name: "payment_service_health"
      url: "https://api.potto.app/health/payments"
      method: "GET"
      timeout: 20
      interval: 300
      expected_status: 200
      
    - name: "auth_service_health"
      url: "https://api.potto.app/health/auth"
      method: "GET"
      timeout: 10
      interval: 180
      expected_status: 200
      
  # Synthetic Monitoring
  synthetic_tests:
    - name: "user_login_flow"
      description: "Test complete user login flow"
      steps:
        - action: "navigate"
          url: "https://app.potto.app/login"
        - action: "fill_form"
          selector: "#email"
          value: "<EMAIL>"
        - action: "fill_form"
          selector: "#password"
          value: "TestPassword123!"
        - action: "click"
          selector: "#login-button"
        - action: "wait_for"
          selector: "#dashboard"
          timeout: 10
          
    - name: "wallet_creation_flow"
      description: "Test wallet creation process"
      frequency: "hourly"
      
    - name: "payment_processing_flow"
      description: "Test payment processing"
      frequency: "every_30_minutes"

# Analytics and Business Intelligence
analytics:
  # User Analytics
  user_analytics:
    # User Engagement Metrics
    engagement:
      - "daily_active_users"
      - "weekly_active_users"
      - "monthly_active_users"
      - "session_duration"
      - "screen_views"
      - "feature_usage"
      
    # User Retention Metrics
    retention:
      - "day_1_retention"
      - "day_7_retention"
      - "day_30_retention"
      - "cohort_analysis"
      
    # User Journey Analytics
    journey:
      - "onboarding_completion_rate"
      - "wallet_creation_funnel"
      - "payment_completion_funnel"
      - "invitation_acceptance_rate"
      
  # Business Analytics
  business_analytics:
    # Financial Metrics
    financial:
      - "total_transaction_volume"
      - "average_transaction_amount"
      - "revenue_per_user"
      - "payment_method_distribution"
      
    # Growth Metrics
    growth:
      - "user_acquisition_rate"
      - "organic_vs_paid_users"
      - "referral_conversion_rate"
      - "viral_coefficient"
      
    # Product Metrics
    product:
      - "feature_adoption_rate"
      - "wallet_usage_patterns"
      - "card_issuance_rate"
      - "group_size_distribution"

# Log Management and Analysis
logging:
  # Log Aggregation
  aggregation:
    # Log Levels
    levels:
      production: "INFO"
      staging: "DEBUG"
      development: "TRACE"
      
    # Log Categories
    categories:
      - "authentication"
      - "payments"
      - "database"
      - "api_requests"
      - "user_actions"
      - "errors"
      - "security"
      
  # Log Analysis
  analysis:
    # Automated Log Analysis
    patterns:
      - name: "suspicious_login_attempts"
        pattern: "failed_login.*rate_limit"
        action: "alert"
        
      - name: "payment_fraud_indicators"
        pattern: "payment.*fraud_score > 0.8"
        action: "alert"
        
      - name: "performance_degradation"
        pattern: "response_time > 5000ms"
        action: "monitor"
        
    # Log Retention
    retention:
      error_logs: "90 days"
      access_logs: "30 days"
      audit_logs: "1 year"
      debug_logs: "7 days"

# Security Monitoring
security:
  # Security Event Monitoring
  events:
    - "failed_authentication_attempts"
    - "privilege_escalation_attempts"
    - "suspicious_api_usage"
    - "data_access_violations"
    - "payment_fraud_attempts"
    
  # Threat Detection
  threat_detection:
    # Anomaly Detection
    anomalies:
      - "unusual_login_patterns"
      - "abnormal_transaction_volumes"
      - "suspicious_device_access"
      - "irregular_api_usage"
      
    # Security Thresholds
    thresholds:
      failed_login_attempts: 5
      api_rate_limit: 1000 # requests per minute
      transaction_velocity: 10 # transactions per minute
      
  # Compliance Monitoring
  compliance:
    # PCI DSS Monitoring
    pci_dss:
      - "card_data_access_logging"
      - "encryption_key_rotation"
      - "access_control_monitoring"
      
    # GDPR Compliance
    gdpr:
      - "data_processing_logging"
      - "consent_tracking"
      - "data_deletion_requests"

# Reporting and Dashboards
reporting:
  # Automated Reports
  automated_reports:
    # Daily Reports
    daily:
      - name: "daily_metrics_summary"
        recipients: ["<EMAIL>"]
        format: "email"
        
      - name: "daily_error_summary"
        recipients: ["<EMAIL>"]
        format: "slack"
        
    # Weekly Reports
    weekly:
      - name: "weekly_business_metrics"
        recipients: ["<EMAIL>"]
        format: "pdf"
        
      - name: "weekly_performance_report"
        recipients: ["<EMAIL>"]
        format: "dashboard_link"
        
    # Monthly Reports
    monthly:
      - name: "monthly_executive_summary"
        recipients: ["<EMAIL>"]
        format: "presentation"
        
  # Custom Dashboards
  dashboards:
    # Operations Dashboard
    operations:
      - "system_health_overview"
      - "error_rates_and_alerts"
      - "performance_metrics"
      - "deployment_status"
      
    # Business Dashboard
    business:
      - "user_growth_metrics"
      - "transaction_analytics"
      - "revenue_tracking"
      - "feature_adoption"
      
    # Security Dashboard
    security:
      - "security_events_overview"
      - "threat_detection_status"
      - "compliance_monitoring"
      - "audit_trail"
