# Potto App Deployment Configuration
# This file defines deployment settings for different environments

# Global Configuration
global:
  app_name: "Potto"
  bundle_id: "com.potto.app"
  version_format: "semantic" # semantic, build_number, timestamp
  
  # Build Configuration
  build:
    flutter_version: "3.19.0"
    dart_version: "3.3.0"
    java_version: "17"
    xcode_version: "15.0"
    
  # Code Signing
  code_signing:
    android:
      keystore_path: "android/app/keystore.jks"
      key_alias: "potto-key"
      store_password_env: "ANDROID_KEYSTORE_PASSWORD"
      key_password_env: "ANDROID_KEY_PASSWORD"
    ios:
      team_id: "YOUR_TEAM_ID"
      certificate_type: "distribution"
      provisioning_profile: "Potto App Store"

# Environment Configurations
environments:
  # Development Environment
  development:
    name: "Potto Dev"
    suffix: ".dev"
    
    # Backend Configuration
    backend:
      supabase_url: "https://dev-project.supabase.co"
      supabase_anon_key_env: "SUPABASE_DEV_ANON_KEY"
      stripe_publishable_key_env: "STRIPE_DEV_PUBLISHABLE_KEY"
      
    # Firebase Configuration
    firebase:
      android_app_id: "1:123456789:android:dev"
      ios_app_id: "1:123456789:ios:dev"
      project_id: "potto-dev"
      
    # Distribution
    distribution:
      android:
        method: "firebase_app_distribution"
        groups: ["internal-testers", "qa-team"]
      ios:
        method: "testflight"
        groups: ["internal-testers"]
        
    # Feature Flags
    features:
      debug_mode: true
      analytics_enabled: false
      crash_reporting: true
      performance_monitoring: false
      
  # Staging Environment
  staging:
    name: "Potto Staging"
    suffix: ".staging"
    
    # Backend Configuration
    backend:
      supabase_url: "https://staging-project.supabase.co"
      supabase_anon_key_env: "SUPABASE_STAGING_ANON_KEY"
      stripe_publishable_key_env: "STRIPE_STAGING_PUBLISHABLE_KEY"
      
    # Firebase Configuration
    firebase:
      android_app_id: "1:123456789:android:staging"
      ios_app_id: "1:123456789:ios:staging"
      project_id: "potto-staging"
      
    # Distribution
    distribution:
      android:
        method: "firebase_app_distribution"
        groups: ["beta-testers", "stakeholders"]
      ios:
        method: "testflight"
        groups: ["beta-testers"]
        
    # Feature Flags
    features:
      debug_mode: false
      analytics_enabled: true
      crash_reporting: true
      performance_monitoring: true
      
  # Production Environment
  production:
    name: "Potto"
    suffix: ""
    
    # Backend Configuration
    backend:
      supabase_url: "https://prod-project.supabase.co"
      supabase_anon_key_env: "SUPABASE_PROD_ANON_KEY"
      stripe_publishable_key_env: "STRIPE_PROD_PUBLISHABLE_KEY"
      
    # Firebase Configuration
    firebase:
      android_app_id: "1:123456789:android:prod"
      ios_app_id: "1:123456789:ios:prod"
      project_id: "potto-prod"
      
    # Distribution
    distribution:
      android:
        method: "google_play_store"
        track: "production"
        rollout_percentage: 100
      ios:
        method: "app_store"
        
    # Feature Flags
    features:
      debug_mode: false
      analytics_enabled: true
      crash_reporting: true
      performance_monitoring: true

# Build Configurations
build_configs:
  # Android Build Configuration
  android:
    # Build Types
    debug:
      minify_enabled: false
      shrink_resources: false
      proguard_enabled: false
      
    release:
      minify_enabled: true
      shrink_resources: true
      proguard_enabled: true
      proguard_files:
        - "android/app/proguard-rules.pro"
        
    # Build Variants
    variants:
      - name: "development"
        application_id_suffix: ".dev"
        version_name_suffix: "-dev"
        
      - name: "staging"
        application_id_suffix: ".staging"
        version_name_suffix: "-staging"
        
      - name: "production"
        # No suffix for production
        
    # Signing Configs
    signing:
      debug:
        store_file: "debug.keystore"
        store_password: "android"
        key_alias: "androiddebugkey"
        key_password: "android"
        
      release:
        store_file_env: "ANDROID_KEYSTORE_FILE"
        store_password_env: "ANDROID_KEYSTORE_PASSWORD"
        key_alias_env: "ANDROID_KEY_ALIAS"
        key_password_env: "ANDROID_KEY_PASSWORD"
        
  # iOS Build Configuration
  ios:
    # Build Configurations
    debug:
      code_sign_identity: "iPhone Developer"
      provisioning_profile: "Development"
      
    release:
      code_sign_identity: "iPhone Distribution"
      provisioning_profile_env: "IOS_PROVISIONING_PROFILE"
      
    # Build Settings
    settings:
      IPHONEOS_DEPLOYMENT_TARGET: "12.0"
      SWIFT_VERSION: "5.0"
      ENABLE_BITCODE: false
      
    # Schemes
    schemes:
      - name: "Runner"
        build_configuration: "Release"
        archive_configuration: "Release"

# Deployment Strategies
deployment:
  # Rollout Strategy
  rollout:
    type: "gradual" # immediate, gradual, canary
    stages:
      - percentage: 1
        duration: "24h"
      - percentage: 10
        duration: "48h"
      - percentage: 50
        duration: "72h"
      - percentage: 100
        
  # Rollback Strategy
  rollback:
    auto_rollback_enabled: true
    rollback_triggers:
      - crash_rate_threshold: 5.0 # percentage
      - error_rate_threshold: 10.0 # percentage
      - user_rating_threshold: 3.0 # minimum rating
      
  # Health Checks
  health_checks:
    - name: "app_startup"
      endpoint: "/health"
      timeout: 30
      
    - name: "api_connectivity"
      endpoint: "/api/health"
      timeout: 10
      
    - name: "payment_service"
      endpoint: "/api/payments/health"
      timeout: 15

# Monitoring Configuration
monitoring:
  # Performance Monitoring
  performance:
    enabled: true
    sample_rate: 0.1 # 10% of sessions
    
  # Crash Reporting
  crash_reporting:
    enabled: true
    upload_symbols: true
    
  # Analytics
  analytics:
    enabled: true
    events:
      - "app_open"
      - "user_signup"
      - "wallet_created"
      - "payment_made"
      - "card_issued"
      
  # Custom Metrics
  custom_metrics:
    - name: "wallet_creation_time"
      type: "histogram"
      
    - name: "payment_success_rate"
      type: "counter"
      
    - name: "active_users"
      type: "gauge"

# Security Configuration
security:
  # Certificate Pinning
  certificate_pinning:
    enabled: true
    certificates:
      - domain: "supabase.co"
        pins: ["sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA="]
      - domain: "stripe.com"
        pins: ["sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB="]
        
  # Network Security
  network_security:
    enforce_https: true
    allow_cleartext_traffic: false
    
  # Code Obfuscation
  obfuscation:
    enabled: true
    exclude_classes:
      - "com.stripe.**"
      - "io.supabase.**"
      
  # Root/Jailbreak Detection
  root_detection:
    enabled: true
    block_rooted_devices: false # Just warn for now
    
# Notification Configuration
notifications:
  # Deployment Notifications
  deployment:
    slack:
      webhook_env: "SLACK_WEBHOOK_URL"
      channels:
        - "#potto-deployments"
        - "#potto-alerts"
        
    email:
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
        
  # Alert Notifications
  alerts:
    pagerduty:
      integration_key_env: "PAGERDUTY_INTEGRATION_KEY"
      
    discord:
      webhook_env: "DISCORD_WEBHOOK_URL"
