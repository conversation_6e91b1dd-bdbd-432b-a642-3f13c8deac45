# Potto App - Deployment & Distribution Guide

This document provides comprehensive instructions for deploying and distributing the Potto app across different environments and platforms.

## 📋 Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [CI/CD Pipeline](#cicd-pipeline)
- [Deployment Process](#deployment-process)
- [App Store Submission](#app-store-submission)
- [Production Monitoring](#production-monitoring)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The Potto app deployment system supports three environments:
- **Development**: For internal testing and development
- **Staging**: For beta testing and stakeholder review
- **Production**: For public app store releases

### Supported Platforms
- **Android**: Google Play Store distribution
- **iOS**: Apple App Store distribution

### Deployment Methods
- **Automated**: Via GitHub Actions CI/CD pipeline
- **Manual**: Using deployment scripts
- **Emergency**: Quick deployment procedures

## 🔧 Prerequisites

### Development Environment
- **Flutter SDK**: 3.19.0 or later
- **Dart SDK**: 3.3.0 or later
- **Git**: Version control
- **Node.js**: For additional tooling (optional)

### Platform-Specific Requirements

#### Android
- **Java JDK**: Version 17
- **Android Studio**: Latest stable version
- **Android SDK**: API level 33+
- **Gradle**: 8.0+

#### iOS (macOS only)
- **Xcode**: 15.0 or later
- **iOS SDK**: 17.0+
- **CocoaPods**: Latest version
- **Apple Developer Account**: For distribution

### Third-Party Services
- **Supabase**: Backend and database
- **Stripe**: Payment processing
- **Firebase**: Analytics and crash reporting
- **GitHub**: Source control and CI/CD

## 🌍 Environment Setup

### 1. Environment Variables

Create environment-specific `.env` files:

#### Development (`.env.development`)
```bash
FLUTTER_ENV=development
SUPABASE_URL=https://dev-project.supabase.co
SUPABASE_ANON_KEY=your_dev_anon_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_dev_key
FIREBASE_PROJECT_ID=potto-dev
```

#### Staging (`.env.staging`)
```bash
FLUTTER_ENV=staging
SUPABASE_URL=https://staging-project.supabase.co
SUPABASE_ANON_KEY=your_staging_anon_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_staging_key
FIREBASE_PROJECT_ID=potto-staging
```

#### Production (`.env.production`)
```bash
FLUTTER_ENV=production
SUPABASE_URL=https://prod-project.supabase.co
SUPABASE_ANON_KEY=your_prod_anon_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_prod_key
FIREBASE_PROJECT_ID=potto-prod
```

### 2. Code Signing Setup

#### Android Code Signing
1. Generate keystore:
```bash
keytool -genkey -v -keystore android/app/keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias potto-key
```

2. Create `android/key.properties`:
```properties
storePassword=your_keystore_password
keyPassword=your_key_password
keyAlias=potto-key
storeFile=keystore.jks
```

#### iOS Code Signing
1. Configure in Xcode:
   - Set Team ID
   - Configure provisioning profiles
   - Set up distribution certificates

2. Update `ios/Runner.xcodeproj` settings

### 3. Firebase Configuration

#### Android
- Download `google-services.json` for each environment
- Place in `android/app/src/{environment}/`

#### iOS
- Download `GoogleService-Info.plist` for each environment
- Add to Xcode project with appropriate targets

## 🚀 CI/CD Pipeline

### GitHub Actions Workflow

The CI/CD pipeline (`.github/workflows/ci.yml`) includes:

1. **Code Analysis**: Linting, formatting, security scanning
2. **Testing**: Unit, widget, integration, and security tests
3. **Building**: Android APK/AAB and iOS builds
4. **Deployment**: Automated distribution to appropriate channels

### Pipeline Triggers
- **Push to `main`**: Production deployment
- **Push to `develop`**: Development deployment
- **Pull Requests**: Testing and validation
- **Release Tags**: Production release

### Required Secrets

Configure these secrets in GitHub repository settings:

```bash
# Android
ANDROID_KEYSTORE_PASSWORD
ANDROID_KEY_PASSWORD
GOOGLE_PLAY_SERVICE_ACCOUNT

# iOS
IOS_CERTIFICATE_PASSWORD
IOS_PROVISIONING_PROFILE
APP_STORE_CONNECT_API_KEY

# Firebase
FIREBASE_SERVICE_ACCOUNT
FIREBASE_APP_ID_ANDROID
FIREBASE_APP_ID_IOS

# Notifications
SLACK_WEBHOOK
DISCORD_WEBHOOK
PAGERDUTY_INTEGRATION_KEY

# Environment Variables
SUPABASE_DEV_ANON_KEY
SUPABASE_STAGING_ANON_KEY
SUPABASE_PROD_ANON_KEY
STRIPE_DEV_PUBLISHABLE_KEY
STRIPE_STAGING_PUBLISHABLE_KEY
STRIPE_PROD_PUBLISHABLE_KEY
```

## 📦 Deployment Process

### Automated Deployment

#### Development Deployment
```bash
# Triggered automatically on push to develop branch
git push origin develop
```

#### Staging Deployment
```bash
# Create staging branch and push
git checkout -b staging
git push origin staging
```

#### Production Deployment
```bash
# Create and push release tag
git tag v1.0.0
git push origin v1.0.0
```

### Manual Deployment

#### Using Deployment Script
```bash
# Development deployment
./scripts/deploy.sh -e development -p both

# Staging deployment
./scripts/deploy.sh -e staging -p both --skip-tests

# Production deployment (dry run first)
./scripts/deploy.sh -e production -p both --dry-run
./scripts/deploy.sh -e production -p both
```

#### Direct Flutter Commands
```bash
# Android
flutter build apk --release --dart-define=FLUTTER_ENV=production
flutter build appbundle --release --dart-define=FLUTTER_ENV=production

# iOS
flutter build ios --release --dart-define=FLUTTER_ENV=production
```

### Deployment Verification

After deployment, verify:
1. **App launches successfully**
2. **Authentication works**
3. **Payment processing functions**
4. **Real-time features work**
5. **Push notifications deliver**

## 🏪 App Store Submission

### Google Play Store

#### Preparation
1. **Build signed AAB**:
```bash
flutter build appbundle --release
```

2. **Upload to Play Console**:
   - Create new release
   - Upload AAB file
   - Add release notes
   - Configure rollout percentage

3. **Store Listing**:
   - Update app description
   - Upload screenshots
   - Set pricing and distribution

#### Review Process
- **Review time**: 1-3 days typically
- **Common issues**: Privacy policy, permissions, content rating
- **Testing**: Google performs automated and manual testing

### Apple App Store

#### Preparation
1. **Build and archive**:
```bash
flutter build ios --release
# Then archive in Xcode
```

2. **Upload to App Store Connect**:
   - Use Xcode or Application Loader
   - Fill out app information
   - Add screenshots and metadata

3. **Submit for Review**:
   - Complete app review information
   - Add review notes if needed
   - Submit for review

#### Review Process
- **Review time**: 1-7 days typically
- **Common issues**: App functionality, design guidelines, privacy
- **Testing**: Apple performs thorough manual testing

### App Store Optimization (ASO)

#### Keywords and Description
- Use relevant keywords in title and description
- Optimize for search terms users might use
- Keep descriptions clear and compelling

#### Screenshots and Media
- Create compelling screenshots showing key features
- Use app preview videos to demonstrate functionality
- Follow platform-specific design guidelines

#### Ratings and Reviews
- Encourage satisfied users to leave reviews
- Respond to user feedback promptly
- Address common issues mentioned in reviews

## 📊 Production Monitoring

### Monitoring Setup

#### Firebase Analytics
- Track user engagement and retention
- Monitor app performance and crashes
- Set up custom events for business metrics

#### Crashlytics
- Automatic crash reporting
- Custom logging for debugging
- Performance monitoring

#### Custom Monitoring
- API response times
- Payment success rates
- User journey analytics
- Business KPIs

### Alerting Configuration

#### Critical Alerts
- App crash rate > 5%
- Payment failure rate > 10%
- API error rate > 15%
- Database connection failures

#### Warning Alerts
- Response time > 2 seconds
- Memory usage > 80%
- User rating < 4.0

#### Notification Channels
- **Slack**: Real-time team notifications
- **Email**: Detailed alert information
- **PagerDuty**: Critical incident escalation

### Health Checks

#### Automated Monitoring
- API endpoint health checks
- Database connectivity tests
- Payment service availability
- Authentication service status

#### Synthetic Testing
- User login flow testing
- Wallet creation process
- Payment processing flow
- End-to-end user journeys

## 🔧 Troubleshooting

### Common Deployment Issues

#### Build Failures
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk --release
```

#### Code Signing Issues
- Verify keystore and certificates are valid
- Check provisioning profiles are up to date
- Ensure bundle IDs match

#### Environment Configuration
- Verify environment variables are set correctly
- Check API keys and secrets are valid
- Confirm Firebase configuration files are present

### Production Issues

#### App Crashes
1. Check Crashlytics for crash reports
2. Review recent code changes
3. Test on affected device types
4. Deploy hotfix if critical

#### Performance Issues
1. Monitor performance metrics
2. Check API response times
3. Review database query performance
4. Optimize critical code paths

#### Payment Issues
1. Check Stripe dashboard for errors
2. Verify webhook endpoints are responding
3. Review payment flow logs
4. Test with different payment methods

### Emergency Procedures

#### Rollback Process
1. **Immediate**: Reduce rollout percentage to 0%
2. **Short-term**: Deploy previous stable version
3. **Long-term**: Fix issues and redeploy

#### Incident Response
1. **Assess**: Determine severity and impact
2. **Communicate**: Notify team and stakeholders
3. **Mitigate**: Implement temporary fixes
4. **Resolve**: Deploy permanent solution
5. **Review**: Conduct post-incident analysis

## 📞 Support and Resources

### Documentation
- [Flutter Documentation](https://docs.flutter.dev/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Stripe Documentation](https://stripe.com/docs)
- [Supabase Documentation](https://supabase.com/docs)

### Team Contacts
- **DevOps Team**: <EMAIL>
- **Backend Team**: <EMAIL>
- **Mobile Team**: <EMAIL>
- **On-Call**: <EMAIL>

### External Support
- **Google Play Console**: [Support](https://support.google.com/googleplay/android-developer/)
- **App Store Connect**: [Support](https://developer.apple.com/support/app-store-connect/)
- **Firebase Support**: [Console](https://firebase.google.com/support/)

---

**Last Updated**: Current Date  
**Version**: 1.0.0  
**Maintained By**: Potto DevOps Team
