name: potto_app
description: A group wallet app with virtual cards for shared spending
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  
  # Backend & Database
  supabase_flutter: ^2.0.0
  
  # Payments
  flutter_stripe: ^10.2.0
  stripe_platform_interface: ^10.0.0
  stripe_android: ^10.0.0
  stripe_ios: ^10.0.0
  
  # HTTP & API
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # UI & Navigation
  go_router: ^12.1.1
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  
  # Utilities
  intl: ^0.19.0
  uuid: ^4.1.0
  shared_preferences: ^2.2.2
  flutter_dotenv: ^5.1.0
  dartz: ^0.10.1
  get_it: ^8.1.0
  
  # Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  flutter_local_notifications: ^16.3.0
  
  # Device Features
  permission_handler: ^11.1.0
  url_launcher: ^6.2.1
  share_plus: ^7.2.1
  
  # Development
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.6
  flutter_secure_storage: ^9.2.4
  local_auth: ^2.3.0
  crypto: ^3.0.6
  pointycastle: ^4.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  
  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  
  # Testing
  mockito: ^5.4.2
  bloc_test: ^9.1.5
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - .env
  
  # TODO: Add Inter font files to assets/fonts/ directory
  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"

flutter_native_splash:
  color: "#FFFFFF"
  image: assets/icons/splash_icon.png
  android_12:
    image: assets/icons/splash_icon.png
    color: "#FFFFFF"
